# KOST2 - Innovative Boarding House Platform

## 🏠 Project Overview

KOST2 is an innovative boarding house (kost) platform with three main user roles: <PERSON><PERSON>, Kost Owner, and User/Searcher. The key innovation is allowing **limited preview without login** but requiring login for detailed information, encouraging user registration while providing initial value.

## ✨ Key Features

### 🔍 **Innovative Preview System**
- **Guest Users**: Browse and search kost with limited information (photos, basic details, general location)
- **Registered Users**: Access full details, contact information, facilities, testimonials, and comparison tools
- **Smart Registration Prompts**: Encourage sign-up when users want more details

### 👥 **Three User Roles**

#### 🏠 **Kost Owners**
- Property management dashboard
- Upload and manage property images
- Real-time notifications when users view properties
- Statistics and analytics
- Room availability management

#### 🔍 **Users/Searchers**
- Advanced search and filtering
- Favorites and comparison system
- Full property details after login
- Contact property owners directly

#### ⚙️ **Administrators**
- User management and moderation
- Property approval and monitoring
- System analytics and reporting
- Platform oversight

## 🛠 Tech Stack

### Frontend
- **React 19** with TypeScript
- **Vite** for build tooling
- **Tailwind CSS** for styling
- **shadcn/ui** for component library
- **React Router** for navigation
- **Axios** for API communication

### Backend
- **Node.js** with Express.js
- **PostgreSQL** database
- **JWT** authentication
- **bcrypt** for password hashing
- **CORS** enabled

### Database
- **PostgreSQL** with UUID primary keys
- Comprehensive schema with proper relationships
- Indexes for performance optimization
- Triggers for automatic timestamp updates

## 📊 Database Schema

The database includes the following main entities:
- `users` - User accounts with role-based access
- `kost_properties` - Property listings
- `rooms` - Individual room details
- `facilities` - Available amenities
- `property_images` - Image management
- `favorites` - User favorites system
- `testimonials` - User reviews
- `notifications` - Real-time notifications
- `property_views` - Analytics tracking

## 🚀 Getting Started

### Prerequisites
- Node.js (v18 or higher)
- PostgreSQL (v13 or higher)
- npm or yarn

### Database Setup

1. **Create PostgreSQL Database**
   ```bash
   createdb kost
   ```

2. **Run Schema Creation**
   ```bash
   psql -d kost -f database/schema.sql
   ```

3. **Insert Seed Data**
   ```bash
   psql -d kost -f database/seed.sql
   ```

### Backend Setup

1. **Install Dependencies**
   ```bash
   cd server
   npm install
   ```

2. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start Development Server**
   ```bash
   npm run dev
   ```

### Frontend Setup

1. **Install Dependencies**
   ```bash
   cd client
   npm install
   ```

2. **Install Additional Packages**
   ```bash
   # React Router
   npm install react-router-dom

   # Tailwind CSS
   npm install -D tailwindcss postcss autoprefixer
   npx tailwindcss init -p

   # shadcn/ui
   npx shadcn-ui@latest init
   ```

3. **Start Development Server**
   ```bash
   npm run dev
   ```

## 🎨 UI/UX Design Principles

### 🎯 **User Experience Flow**
1. **Landing Page** - Clear value proposition and role selection
2. **Guest Browsing** - Attractive previews with strategic information limits
3. **Registration Incentive** - Compelling reasons to sign up for full access
4. **Authenticated Experience** - Rich, detailed information and interactive features
5. **Owner Dashboard** - Comprehensive property management tools
6. **Admin Panel** - Powerful oversight and analytics tools

### 📱 **Responsive Design**
- Mobile-first approach
- Tailwind CSS utility classes
- Dark mode support
- Accessible design with ARIA attributes

### 🎨 **Component Architecture**
- Reusable shadcn/ui components
- Consistent design system
- TypeScript for type safety
- Modular component structure

## 🔐 Security Features

- **JWT Authentication** with refresh tokens
- **Role-based Access Control** (RBAC)
- **Password Hashing** with bcrypt
- **Input Validation** and sanitization
- **CORS Protection**
- **Rate Limiting**
- **SQL Injection Prevention**

## 📈 Performance Optimizations

- **Database Indexing** for fast queries
- **Connection Pooling** for database efficiency
- **Lazy Loading** for React components
- **Image Optimization** for faster loading
- **Caching Strategies** for frequently accessed data

## 🔔 Real-time Features

- **Owner Notifications** when users view properties
- **Live Updates** for room availability
- **Real-time Statistics** for property owners

## 📝 Development Roadmap

### Phase 1: Foundation ✅
- [x] Database schema design
- [x] Database setup and configuration
- [ ] Backend API structure
- [ ] Frontend architecture setup

### Phase 2: Authentication & Core Features
- [ ] JWT authentication system
- [ ] User registration and login
- [ ] Role-based routing
- [ ] Basic property CRUD operations

### Phase 3: Guest Experience
- [ ] Limited preview implementation
- [ ] Search and filtering
- [ ] Registration prompts
- [ ] Property listing pages

### Phase 4: User Features
- [ ] User dashboard
- [ ] Favorites system
- [ ] Property comparison
- [ ] Detailed property views

### Phase 5: Owner Features
- [ ] Owner dashboard
- [ ] Property management
- [ ] Image upload system
- [ ] Statistics and analytics

### Phase 6: Admin Features
- [ ] Admin dashboard
- [ ] User management
- [ ] Property moderation
- [ ] System analytics

### Phase 7: Advanced Features
- [ ] Real-time notifications
- [ ] Email notifications
- [ ] Advanced search filters
- [ ] Mobile optimization

### Phase 8: Testing & Deployment
- [ ] Unit testing
- [ ] Integration testing
- [ ] End-to-end testing
- [ ] Production deployment

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 📞 Support

For support and questions, please contact the development team.

---

**Built with ❤️ for the Indonesian boarding house community**
