# KOST2 Testing Guide

This document provides comprehensive information about testing the KOST2 platform, including setup, running tests, and testing strategies.

## Table of Contents

- [Testing Overview](#testing-overview)
- [Backend Testing](#backend-testing)
- [Frontend Testing](#frontend-testing)
- [Test Coverage](#test-coverage)
- [Continuous Integration](#continuous-integration)
- [Manual Testing](#manual-testing)
- [Performance Testing](#performance-testing)

## Testing Overview

The KOST2 platform uses a comprehensive testing strategy that includes:

- **Unit Tests**: Testing individual functions and components
- **Integration Tests**: Testing API endpoints and component interactions
- **End-to-End Tests**: Testing complete user workflows
- **Performance Tests**: Testing system performance under load
- **Security Tests**: Testing authentication and authorization

## Backend Testing

### Setup

The backend uses **Jest** and **Supertest** for testing.

```bash
cd server
npm install
npm run test
```

### Test Structure

```
server/
├── tests/
│   ├── setup.js              # Test configuration and helpers
│   ├── auth.test.js           # Authentication tests
│   ├── properties.test.js     # Properties API tests
│   ├── users.test.js          # Users API tests
│   └── admin.test.js          # Admin API tests
├── jest.config.js             # Jest configuration
└── package.json
```

### Running Backend Tests

```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run tests for CI
npm run test:ci
```

### Test Categories

#### Authentication Tests
- User registration with validation
- User login with credentials
- JWT token verification
- Password security
- Session management

#### Properties API Tests
- Property listing with pagination
- Property filtering and search
- Property details retrieval
- View tracking
- Guest vs authenticated access

#### Users API Tests
- Favorites management
- Notification system
- Profile updates
- User preferences

#### Admin API Tests
- User management
- Property moderation
- System analytics
- Role-based access control

### Test Helpers

The test suite includes several helper functions:

```javascript
// Create test user
const testUser = await createTestUser({
  email: '<EMAIL>',
  role: 'user'
});

// Create test property
const testProperty = await createTestProperty(ownerId, {
  name: 'Test Property',
  city: 'Test City'
});

// Generate JWT token
const token = generateTestToken(userId);

// Make authenticated request
const response = await authenticatedRequest(request(app), token)
  .get('/api/properties')
  .expect(200);
```

## Frontend Testing

### Setup

The frontend uses **Vitest** and **React Testing Library**.

```bash
cd client
npm install
npm run test
```

### Test Structure

```
client/src/
├── tests/
│   ├── setup.ts                    # Test configuration
│   ├── components/
│   │   ├── Navbar.test.tsx         # Navigation component tests
│   │   ├── PropertyCard.test.tsx   # Property card tests
│   │   └── LoginForm.test.tsx      # Form component tests
│   ├── contexts/
│   │   ├── AuthContext.test.tsx    # Authentication context tests
│   │   └── SocketContext.test.tsx  # Socket context tests
│   ├── lib/
│   │   ├── api.test.ts             # API utility tests
│   │   └── utils.test.ts           # Utility function tests
│   └── pages/
│       ├── HomePage.test.tsx       # Page component tests
│       └── PropertiesPage.test.tsx # Properties page tests
├── vitest.config.ts                # Vitest configuration
└── package.json
```

### Running Frontend Tests

```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run tests with UI
npm run test:ui
```

### Test Categories

#### Component Tests
- Rendering behavior
- User interactions
- Props handling
- State management
- Event handling

#### Context Tests
- Authentication flow
- Socket connections
- State updates
- Error handling

#### API Tests
- HTTP requests
- Response handling
- Error scenarios
- Authentication headers

#### Integration Tests
- Component interactions
- Context providers
- Route navigation
- Form submissions

### Test Utilities

```typescript
// Mock user data
const mockUser = createMockUser({
  role: 'owner',
  full_name: 'Test Owner'
});

// Mock property data
const mockProperty = createMockProperty({
  name: 'Test Kost',
  city: 'Jakarta'
});

// Setup fetch mock
setupFetchMock(createMockApiResponse(data));

// Render with providers
const renderWithProviders = (component) => {
  return render(
    <AuthProvider>
      <SocketProvider>
        <BrowserRouter>
          {component}
        </BrowserRouter>
      </SocketProvider>
    </AuthProvider>
  );
};
```

## Test Coverage

### Coverage Goals

- **Backend**: Minimum 80% code coverage
- **Frontend**: Minimum 75% code coverage
- **Critical paths**: 95% coverage for authentication and payment flows

### Coverage Reports

```bash
# Backend coverage
cd server && npm run test:coverage

# Frontend coverage
cd client && npm run test:coverage
```

Coverage reports are generated in:
- `server/coverage/` - Backend coverage
- `client/coverage/` - Frontend coverage

### Coverage Analysis

Key areas monitored for coverage:

1. **Authentication & Authorization**
2. **API Endpoints**
3. **Database Operations**
4. **User Interface Components**
5. **Business Logic**
6. **Error Handling**

## Continuous Integration

### GitHub Actions

The project includes CI/CD workflows for automated testing:

```yaml
# .github/workflows/test.yml
name: Test Suite
on: [push, pull_request]
jobs:
  backend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: cd server && npm ci
      - run: cd server && npm run test:ci
      
  frontend-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: cd client && npm ci
      - run: cd client && npm run test:coverage
```

### Pre-commit Hooks

```bash
# Install pre-commit hooks
npm install -g husky
npx husky install

# Add test hook
npx husky add .husky/pre-commit "npm run test"
```

## Manual Testing

### Test Scenarios

#### User Registration & Authentication
1. Register new user with valid data
2. Register with invalid email format
3. Register with weak password
4. Login with correct credentials
5. Login with incorrect credentials
6. Password reset flow
7. Email verification

#### Property Search & Browsing
1. Browse properties as guest
2. Filter by location, type, price
3. Search by keywords
4. View property details
5. Pagination functionality
6. Mobile responsiveness

#### Favorites Management
1. Add property to favorites
2. Remove from favorites
3. View favorites list
4. Bulk operations
5. Favorites persistence

#### Owner Dashboard
1. Property management
2. Analytics viewing
3. Real-time notifications
4. Property creation/editing

#### Admin Functions
1. User management
2. Property moderation
3. System analytics
4. Role-based access

### Browser Testing

Test on multiple browsers:
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

### Device Testing

Test on various devices:
- Desktop (1920x1080, 1366x768)
- Tablet (768x1024, 1024x768)
- Mobile (375x667, 414x896, 360x640)

## Performance Testing

### Load Testing

Use tools like Artillery or k6 for load testing:

```bash
# Install Artillery
npm install -g artillery

# Run load test
artillery run load-test.yml
```

### Performance Metrics

Monitor key metrics:
- Response time < 200ms for API calls
- Page load time < 3 seconds
- Time to interactive < 5 seconds
- Core Web Vitals compliance

### Database Performance

- Query optimization
- Index usage analysis
- Connection pooling
- Cache hit rates

## Security Testing

### Authentication Security
- JWT token validation
- Password hashing verification
- Session management
- CSRF protection

### Authorization Testing
- Role-based access control
- Resource ownership validation
- API endpoint protection
- Admin privilege escalation

### Input Validation
- SQL injection prevention
- XSS protection
- File upload security
- Rate limiting

## Best Practices

### Writing Tests

1. **Descriptive test names**: Use clear, descriptive test names
2. **Arrange-Act-Assert**: Follow the AAA pattern
3. **Test isolation**: Each test should be independent
4. **Mock external dependencies**: Use mocks for external services
5. **Test edge cases**: Include boundary conditions and error scenarios

### Test Maintenance

1. **Regular updates**: Keep tests updated with code changes
2. **Remove obsolete tests**: Clean up tests for removed features
3. **Refactor test code**: Apply DRY principles to test code
4. **Documentation**: Document complex test scenarios

### Debugging Tests

1. **Use descriptive assertions**: Make test failures clear
2. **Add debug output**: Use console.log for debugging
3. **Run tests in isolation**: Debug failing tests individually
4. **Check test data**: Verify test data setup and cleanup

## Troubleshooting

### Common Issues

1. **Database connection errors**: Ensure test database is running
2. **Authentication failures**: Check JWT secret configuration
3. **Timeout errors**: Increase timeout for slow operations
4. **Mock issues**: Verify mock setup and cleanup

### Debug Commands

```bash
# Run specific test file
npm test -- auth.test.js

# Run tests with verbose output
npm test -- --verbose

# Run tests in debug mode
node --inspect-brk node_modules/.bin/jest --runInBand

# Clear Jest cache
npm test -- --clearCache
```

## Conclusion

This comprehensive testing strategy ensures the KOST2 platform is reliable, secure, and performant. Regular testing helps maintain code quality and prevents regressions as the platform evolves.

For questions or issues with testing, please refer to the project documentation or contact the development team.
