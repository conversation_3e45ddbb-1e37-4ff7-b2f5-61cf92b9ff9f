import React, { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import NotificationBadge from '../ui/NotificationBadge'
import {
  Home,
  Building2,
  Heart,
  User,
  LogOut,
  Menu,
  X,
  Search,
  Users
} from 'lucide-react'

const Navbar: React.FC = () => {
  const { isAuthenticated, user, logout } = useAuth()
  const navigate = useNavigate()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)

  const handleLogout = () => {
    logout()
    navigate('/')
    setIsUserMenuOpen(false)
  }

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen)
  }

  const toggleUserMenu = () => {
    setIsUserMenuOpen(!isUserMenuOpen)
  }

  return (
    <nav className="bg-white shadow-sm border-b sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <Building2 className="h-8 w-8 text-primary" />
            <span className="text-xl font-bold text-gray-900">KOST2</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link 
              to="/" 
              className="flex items-center space-x-1 text-gray-700 hover:text-primary transition-colors"
            >
              <Home className="h-4 w-4" />
              <span>Beranda</span>
            </Link>
            <Link 
              to="/properties" 
              className="flex items-center space-x-1 text-gray-700 hover:text-primary transition-colors"
            >
              <Search className="h-4 w-4" />
              <span>Cari Kost</span>
            </Link>
            
            {isAuthenticated && (
              <Link 
                to="/favorites" 
                className="flex items-center space-x-1 text-gray-700 hover:text-primary transition-colors"
              >
                <Heart className="h-4 w-4" />
                <span>Favorit</span>
              </Link>
            )}
          </div>

          {/* Desktop Auth Section */}
          <div className="hidden md:flex items-center space-x-4">
            {isAuthenticated ? (
              <div className="flex items-center space-x-4">
                <Link
                  to="/notifications"
                  className="p-2 text-gray-700 hover:text-primary transition-colors relative"
                >
                  <NotificationBadge />
                </Link>
                
                <div className="relative">
                  <button
                    onClick={toggleUserMenu}
                    className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <div className="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-medium">
                      {user?.full_name?.charAt(0).toUpperCase()}
                    </div>
                    <span className="text-gray-700">{user?.full_name}</span>
                  </button>

                  {isUserMenuOpen && (
                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                      <Link
                        to={
                          user?.role === 'admin' ? "/admin/dashboard" :
                          user?.role === 'owner' ? "/owner/dashboard" :
                          "/dashboard"
                        }
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <User className="inline h-4 w-4 mr-2" />
                        Dashboard
                      </Link>
                      {user?.role === 'owner' && (
                        <Link
                          to="/owner/properties"
                          className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                          onClick={() => setIsUserMenuOpen(false)}
                        >
                          <Building2 className="inline h-4 w-4 mr-2" />
                          Kelola Kost
                        </Link>
                      )}
                      {user?.role === 'admin' && (
                        <>
                          <Link
                            to="/admin/users"
                            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            onClick={() => setIsUserMenuOpen(false)}
                          >
                            <Users className="inline h-4 w-4 mr-2" />
                            Kelola Pengguna
                          </Link>
                          <Link
                            to="/admin/properties"
                            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            onClick={() => setIsUserMenuOpen(false)}
                          >
                            <Building2 className="inline h-4 w-4 mr-2" />
                            Kelola Properti
                          </Link>
                        </>
                      )}
                      <Link
                        to="/profile"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <User className="inline h-4 w-4 mr-2" />
                        Profil
                      </Link>
                      <button
                        onClick={handleLogout}
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <LogOut className="inline h-4 w-4 mr-2" />
                        Keluar
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-4">
                <Link to="/login" className="text-gray-700 hover:text-primary transition-colors">
                  Masuk
                </Link>
                <Link to="/register" className="btn-primary">
                  Daftar
                </Link>
              </div>
            )}
          </div>

          {/* Mobile menu button */}
          <button
            onClick={toggleMobileMenu}
            className="md:hidden p-2 rounded-md text-gray-700 hover:text-primary transition-colors"
            aria-label={isMobileMenuOpen ? "Close menu" : "Open menu"}
            aria-expanded={isMobileMenuOpen}
            aria-controls="mobile-menu"
          >
            {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div id="mobile-menu" className="md:hidden py-4 border-t">
            <div className="flex flex-col space-y-4">
              <Link 
                to="/" 
                className="flex items-center space-x-2 text-gray-700 hover:text-primary transition-colors"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <Home className="h-4 w-4" />
                <span>Beranda</span>
              </Link>
              <Link 
                to="/properties" 
                className="flex items-center space-x-2 text-gray-700 hover:text-primary transition-colors"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <Search className="h-4 w-4" />
                <span>Cari Kost</span>
              </Link>
              
              {isAuthenticated ? (
                <>
                  <Link 
                    to="/favorites" 
                    className="flex items-center space-x-2 text-gray-700 hover:text-primary transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <Heart className="h-4 w-4" />
                    <span>Favorit</span>
                  </Link>
                  <Link
                    to="/notifications"
                    className="flex items-center space-x-2 text-gray-700 hover:text-primary transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <NotificationBadge className="h-4 w-4" />
                    <span>Notifikasi</span>
                  </Link>
                  <Link
                    to={
                      user?.role === 'admin' ? "/admin/dashboard" :
                      user?.role === 'owner' ? "/owner/dashboard" :
                      "/dashboard"
                    }
                    className="flex items-center space-x-2 text-gray-700 hover:text-primary transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <User className="h-4 w-4" />
                    <span>Dashboard</span>
                  </Link>
                  {user?.role === 'owner' && (
                    <Link
                      to="/owner/properties"
                      className="flex items-center space-x-2 text-gray-700 hover:text-primary transition-colors"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <Building2 className="h-4 w-4" />
                      <span>Kelola Kost</span>
                    </Link>
                  )}
                  {user?.role === 'admin' && (
                    <>
                      <Link
                        to="/admin/users"
                        className="flex items-center space-x-2 text-gray-700 hover:text-primary transition-colors"
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        <Users className="h-4 w-4" />
                        <span>Kelola Pengguna</span>
                      </Link>
                      <Link
                        to="/admin/properties"
                        className="flex items-center space-x-2 text-gray-700 hover:text-primary transition-colors"
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        <Building2 className="h-4 w-4" />
                        <span>Kelola Properti</span>
                      </Link>
                    </>
                  )}
                  <Link 
                    to="/profile" 
                    className="flex items-center space-x-2 text-gray-700 hover:text-primary transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <User className="h-4 w-4" />
                    <span>Profil</span>
                  </Link>
                  <button
                    onClick={handleLogout}
                    className="flex items-center space-x-2 text-gray-700 hover:text-primary transition-colors text-left"
                  >
                    <LogOut className="h-4 w-4" />
                    <span>Keluar</span>
                  </button>
                </>
              ) : (
                <div className="flex flex-col space-y-4 pt-4 border-t">
                  <Link 
                    to="/login" 
                    className="text-gray-700 hover:text-primary transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Masuk
                  </Link>
                  <Link 
                    to="/register" 
                    className="btn-primary inline-block text-center"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    Daftar
                  </Link>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}

export default Navbar
