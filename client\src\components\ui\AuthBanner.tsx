import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { AlertCircle, ChevronRight } from 'lucide-react'

interface AuthBannerProps {
  title?: string
  message?: string
  actionText?: string
  variant?: 'orange' | 'blue' | 'green'
}

const AuthBanner: React.FC<AuthBannerProps> = ({
  title = "Preview Terbatas",
  message = "Anda melihat informasi terbatas. Daftar untuk akses penuh ke detail kost dan kontak pemilik.",
  actionText = "Daftar Gratis",
  variant = "orange"
}) => {
  const variantClasses = {
    orange: {
      container: "bg-orange-50 border-orange-200",
      icon: "text-orange-500",
      title: "text-orange-800",
      message: "text-orange-700",
      button: "bg-orange-500 hover:bg-orange-600"
    },
    blue: {
      container: "bg-blue-50 border-blue-200",
      icon: "text-blue-500",
      title: "text-blue-800",
      message: "text-blue-700",
      button: "bg-blue-500 hover:bg-blue-600"
    },
    green: {
      container: "bg-green-50 border-green-200",
      icon: "text-green-500",
      title: "text-green-800",
      message: "text-green-700",
      button: "bg-green-500 hover:bg-green-600"
    }
  }

  const classes = variantClasses[variant]

  return (
    <div className={`${classes.container} border rounded-lg p-4 mb-6`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <AlertCircle className={`h-5 w-5 ${classes.icon} mr-3`} />
          <div>
            <p className={`${classes.title} font-medium`}>{title}</p>
            <p className={`${classes.message} text-sm`}>
              {message}
            </p>
          </div>
        </div>
        <Link
          to="/register"
          className={`${classes.button} text-white px-4 py-2 rounded-md transition-colors flex items-center`}
        >
          {actionText}
          <ChevronRight className="h-4 w-4 ml-1" />
        </Link>
      </div>
    </div>
  )
}

export default AuthBanner
