import React, { useState, useEffect } from 'react'
import { Bell } from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { usersApi } from '../../lib/api'

interface NotificationBadgeProps {
  className?: string
}

const NotificationBadge: React.FC<NotificationBadgeProps> = ({ className = '' }) => {
  const { isAuthenticated } = useAuth()
  const [unreadCount, setUnreadCount] = useState(0)

  useEffect(() => {
    if (isAuthenticated) {
      fetchUnreadCount()
    }
  }, [isAuthenticated])

  useEffect(() => {
    // Listen for new notifications from socket events
    const handleNewNotification = () => {
      setUnreadCount(prev => prev + 1)
    }

    window.addEventListener('newNotification', handleNewNotification)

    return () => {
      window.removeEventListener('newNotification', handleNewNotification)
    }
  }, [])

  const fetchUnreadCount = async () => {
    try {
      const response = await usersApi.getNotifications({ limit: 1 })
      setUnreadCount(response.data.data.unread_count || 0)
    } catch (error) {
      console.error('Error fetching unread count:', error)
    }
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <div className={`relative ${className}`}>
      <Bell className="h-5 w-5" />
      {unreadCount > 0 && (
        <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium">
          {unreadCount > 99 ? '99+' : unreadCount}
        </span>
      )}
    </div>
  )
}

export default NotificationBadge
