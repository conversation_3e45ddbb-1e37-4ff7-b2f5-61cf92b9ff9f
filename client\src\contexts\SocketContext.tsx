import React, { createContext, useContext, useEffect, useState, useRef } from 'react'
import { io, Socket } from 'socket.io-client'
import { useAuth } from './AuthContext'
import { toast } from 'sonner'

interface SocketContextType {
  socket: Socket | null
  isConnected: boolean
  emitPropertyView: (propertyId: string) => void
  emitPropertyFavorite: (propertyId: string, action: 'add' | 'remove') => void
  emitUserRegistration: (userId: string, userRole: string) => void
  emitPropertyCreation: (propertyId: string, ownerId: string) => void
}

const SocketContext = createContext<SocketContextType | undefined>(undefined)

export const useSocket = () => {
  const context = useContext(SocketContext)
  if (context === undefined) {
    throw new Error('useSocket must be used within a SocketProvider')
  }
  return context
}

interface SocketProviderProps {
  children: React.ReactNode
}

export const SocketProvider: React.FC<SocketProviderProps> = ({ children }) => {
  const { user, token } = useAuth()
  const [socket, setSocket] = useState<Socket | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const reconnectAttempts = useRef(0)
  const maxReconnectAttempts = 5

  useEffect(() => {
    if (user && token) {
      connectSocket()
    } else {
      disconnectSocket()
    }

    return () => {
      disconnectSocket()
    }
  }, [user, token])

  const connectSocket = () => {
    if (socket?.connected) return

    const newSocket = io(import.meta.env.VITE_SERVER_URL || 'http://localhost:5000', {
      auth: {
        token: token
      },
      transports: ['websocket', 'polling']
    })

    newSocket.on('connect', () => {
      console.log('Connected to server')
      setIsConnected(true)
      reconnectAttempts.current = 0
      
      if (user?.role === 'owner') {
        toast.success('Notifikasi real-time aktif', {
          description: 'Anda akan menerima notifikasi saat ada yang melihat properti Anda'
        })
      }
    })

    newSocket.on('disconnect', () => {
      console.log('Disconnected from server')
      setIsConnected(false)
    })

    newSocket.on('connect_error', (error) => {
      console.error('Connection error:', error)
      setIsConnected(false)
      
      reconnectAttempts.current++
      if (reconnectAttempts.current >= maxReconnectAttempts) {
        toast.error('Koneksi real-time gagal', {
          description: 'Tidak dapat terhubung ke server untuk notifikasi real-time'
        })
      }
    })

    // Listen for new notifications
    newSocket.on('new_notification', (data) => {
      const { notification, type } = data
      
      if (type === 'property_view') {
        toast.info('Properti Dilihat!', {
          description: notification.message,
          action: {
            label: 'Lihat',
            onClick: () => window.location.href = '/notifications'
          }
        })
      } else if (type === 'property_favorite') {
        toast.success('Properti Difavoritkan!', {
          description: notification.message,
          action: {
            label: 'Lihat',
            onClick: () => window.location.href = '/notifications'
          }
        })
      }

      // Trigger a custom event for components to listen to
      window.dispatchEvent(new CustomEvent('newNotification', { detail: notification }))
    })

    // Listen for property analytics updates
    newSocket.on('property_analytics_update', (data) => {
      const { property_id, total_views, total_favorites, latest_viewer, latest_action, user_name } = data
      
      // Trigger a custom event for analytics components
      window.dispatchEvent(new CustomEvent('propertyAnalyticsUpdate', { 
        detail: { 
          propertyId: property_id, 
          totalViews: total_views, 
          totalFavorites: total_favorites,
          latestViewer: latest_viewer,
          latestAction: latest_action,
          userName: user_name
        } 
      }))
    })

    // Listen for new user registrations (admin only)
    newSocket.on('new_user_registration', (data) => {
      if (user?.role === 'admin') {
        toast.info('Pengguna Baru Terdaftar!', {
          description: `${data.user.full_name} (${data.user.role}) bergabung`,
          action: {
            label: 'Lihat',
            onClick: () => window.location.href = '/admin/users'
          }
        })
      }
    })

    // Listen for new property creations (admin only)
    newSocket.on('new_property_created', (data) => {
      if (user?.role === 'admin') {
        toast.info('Properti Baru Ditambahkan!', {
          description: `${data.property.name} di ${data.property.city} oleh ${data.property.owner_name}`,
          action: {
            label: 'Lihat',
            onClick: () => window.location.href = '/admin/properties'
          }
        })
      }
    })

    setSocket(newSocket)
  }

  const disconnectSocket = () => {
    if (socket) {
      socket.disconnect()
      setSocket(null)
      setIsConnected(false)
    }
  }

  const emitPropertyView = (propertyId: string) => {
    if (socket && user) {
      socket.emit('property_viewed', {
        propertyId,
        viewerId: user.id
      })
    }
  }

  const emitPropertyFavorite = (propertyId: string, action: 'add' | 'remove') => {
    if (socket && user) {
      socket.emit('property_favorited', {
        propertyId,
        userId: user.id,
        action
      })
    }
  }

  const emitUserRegistration = (userId: string, userRole: string) => {
    if (socket) {
      socket.emit('user_registered', {
        userId,
        userRole
      })
    }
  }

  const emitPropertyCreation = (propertyId: string, ownerId: string) => {
    if (socket) {
      socket.emit('property_created', {
        propertyId,
        ownerId
      })
    }
  }

  const value: SocketContextType = {
    socket,
    isConnected,
    emitPropertyView,
    emitPropertyFavorite,
    emitUserRegistration,
    emitPropertyCreation
  }

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  )
}
