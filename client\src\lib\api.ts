import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api'

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Types
export interface User {
  id: string
  email: string
  full_name: string
  phone?: string
  role: 'admin' | 'owner' | 'user'
  is_active: boolean
  email_verified: boolean
  created_at: string
}

export interface Property {
  id: string
  name: string
  description: string
  address: string
  city: string
  province: string
  latitude?: number
  longitude?: number
  property_type: 'putra' | 'putri' | 'campur'
  total_rooms: number
  available_rooms: number
  min_price: number
  max_price: number
  main_image?: string
  avg_rating: number
  review_count: number
  owner_name?: string
  owner_phone?: string
  owner_email?: string
  facilities: Facility[]
  images?: PropertyImage[]
  rooms?: Room[]
  testimonials?: Testimonial[]
  is_favorited?: boolean
  facilities_limited?: boolean
  created_at: string
}

export interface Room {
  id: string
  room_number: string
  room_type: string
  price_monthly: number
  size_sqm: number
  has_ac: boolean
  has_wifi: boolean
  has_private_bathroom: boolean
  has_wardrobe: boolean
  has_desk: boolean
  status: 'available' | 'occupied' | 'maintenance'
}

export interface Facility {
  id: string
  name: string
  icon: string
  category: 'room' | 'common' | 'security' | 'parking'
  description?: string
}

export interface PropertyImage {
  id: string
  image_url: string
  image_type: 'main' | 'room' | 'facility' | 'exterior'
  alt_text?: string
  sort_order: number
}

export interface Testimonial {
  id: string
  rating: number
  comment: string
  user_name: string
  created_at: string
}

export interface Notification {
  id: string
  type: 'view_notification' | 'favorite_added' | 'system'
  title: string
  message: string
  is_read: boolean
  property_name?: string
  created_at: string
}

export interface ApiResponse<T> {
  success: boolean
  data: T
  error?: {
    message: string
    details?: any[]
  }
}

export interface PaginatedResponse<T> {
  success: boolean
  data: {
    [key: string]: T[]
    pagination: {
      current_page: number
      total_pages: number
      total_items: number
      items_per_page: number
      has_next: boolean
      has_prev: boolean
    }
    is_authenticated?: boolean
    message?: string
  }
}

// Auth API
export const authApi = {
  register: (data: {
    email: string
    password: string
    full_name: string
    phone?: string
    role?: 'user' | 'owner'
  }) => api.post<ApiResponse<{ token: string; user: User }>>('/auth/register', data),

  login: (data: { email: string; password: string }) =>
    api.post<ApiResponse<{ token: string; user: User }>>('/auth/login', data),

  getMe: () => api.get<ApiResponse<{ user: User }>>('/auth/me'),

  updateProfile: (data: { full_name?: string; phone?: string }) =>
    api.put<ApiResponse<{ user: User }>>('/auth/profile', data),

  changePassword: (data: { current_password: string; new_password: string }) =>
    api.put<ApiResponse<{ message: string }>>('/auth/password', data),
}

// Properties API
export const propertiesApi = {
  getAll: (params?: {
    page?: number
    limit?: number
    city?: string
    property_type?: string
    min_price?: number
    max_price?: number
    search?: string
  }) => api.get<PaginatedResponse<Property>>('/properties', { params }),

  getById: (id: string) => api.get<ApiResponse<{ property: Property }>>(`/properties/${id}`),
}

// Users API
export const usersApi = {
  getFavorites: () => api.get<ApiResponse<{ favorites: Property[] }>>('/users/favorites'),

  addFavorite: (propertyId: string) =>
    api.post<ApiResponse<{ message: string }>>(`/users/favorites/${propertyId}`),

  removeFavorite: (propertyId: string) =>
    api.delete<ApiResponse<{ message: string }>>(`/users/favorites/${propertyId}`),

  getNotifications: (params?: { page?: number; limit?: number }) =>
    api.get<ApiResponse<{ notifications: Notification[]; unread_count: number }>>(
      '/users/notifications',
      { params }
    ),

  markNotificationRead: (id: string) =>
    api.put<ApiResponse<{ message: string }>>(`/users/notifications/${id}/read`),

  markAllNotificationsRead: () =>
    api.put<ApiResponse<{ message: string }>>('/users/notifications/read-all'),
}

// Facilities API
export const facilitiesApi = {
  getAll: () => api.get<ApiResponse<{ facilities: Facility[]; facilities_by_category: Record<string, Facility[]> }>>('/facilities'),

  getByCategory: (category: string) =>
    api.get<ApiResponse<{ facilities: Facility[]; category: string }>>(`/facilities/category/${category}`),
}

export default api
