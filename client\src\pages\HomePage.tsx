import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { Search, MapPin, Shield, Star, Users, Building2 } from 'lucide-react'

const HomePage: React.FC = () => {
  return (
    <main className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary/10 via-primary/5 to-transparent py-20" aria-label="Hero section">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Temukan <span className="text-primary">Kost Impian</span> Anda
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Platform inovatif untuk mencari kost dengan preview terbatas. 
              Daftar sekarang untuk akses penuh ke informasi properti dan fitur eksklusif.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/properties" className="btn-primary text-lg px-8 py-3">
                <Search className="inline h-5 w-5 mr-2" />
                Mulai Pencarian
              </Link>
              <Link to="/register" className="btn-secondary text-lg px-8 py-3">
                Daftar Gratis
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Mengapa Memilih KOST2?
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Kami menyediakan pengalaman pencarian kost yang inovatif dengan fitur-fitur unggulan
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white p-8 rounded-lg shadow-sm text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Search className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Preview Terbatas
              </h3>
              <p className="text-gray-600">
                Lihat informasi dasar kost tanpa login. Daftar untuk akses penuh ke detail lengkap dan kontak pemilik.
              </p>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Terverifikasi
              </h3>
              <p className="text-gray-600">
                Semua properti telah diverifikasi oleh tim kami untuk memastikan kualitas dan keamanan.
              </p>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-sm text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Review Asli
              </h3>
              <p className="text-gray-600">
                Baca review dari penghuni sebelumnya untuk mendapatkan gambaran yang jelas tentang kost.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Cara Kerja KOST2
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Proses sederhana untuk menemukan kost yang tepat untuk Anda
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                1
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Jelajahi Kost
              </h3>
              <p className="text-gray-600">
                Lihat preview kost dengan informasi dasar tanpa perlu login
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                2
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Daftar Gratis
              </h3>
              <p className="text-gray-600">
                Buat akun untuk mengakses detail lengkap dan fitur eksklusif
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                3
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Bandingkan & Pilih
              </h3>
              <p className="text-gray-600">
                Gunakan fitur perbandingan dan favorit untuk memilih kost terbaik
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary text-white rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                4
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Hubungi Pemilik
              </h3>
              <p className="text-gray-600">
                Dapatkan kontak langsung pemilik kost untuk proses selanjutnya
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-primary text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold mb-2">500+</div>
              <div className="text-primary-foreground/80">Kost Tersedia</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">1000+</div>
              <div className="text-primary-foreground/80">Pengguna Aktif</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">50+</div>
              <div className="text-primary-foreground/80">Kota</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">4.8</div>
              <div className="text-primary-foreground/80">Rating Rata-rata</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="bg-gradient-to-r from-primary to-primary/80 rounded-2xl p-12 text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Siap Menemukan Kost Impian Anda?
            </h2>
            <p className="text-xl mb-8 text-primary-foreground/90">
              Bergabunglah dengan ribuan pengguna yang telah menemukan kost terbaik melalui KOST2
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/register" className="bg-white text-primary hover:bg-gray-100 px-8 py-3 rounded-md font-medium transition-colors">
                <Users className="inline h-5 w-5 mr-2" />
                Daftar Sebagai Pencari
              </Link>
              <Link to="/register" className="bg-primary-foreground/20 text-white hover:bg-primary-foreground/30 px-8 py-3 rounded-md font-medium transition-colors">
                <Building2 className="inline h-5 w-5 mr-2" />
                Daftar Sebagai Pemilik
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}

export default HomePage
