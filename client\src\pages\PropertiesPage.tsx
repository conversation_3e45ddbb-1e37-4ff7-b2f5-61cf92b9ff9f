import React, { useState, useEffect } from 'react'
import { useSearchParams, Link } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { useSocket } from '../contexts/SocketContext'
import { propertiesApi, usersApi, type Property } from '../lib/api'
import { formatPrice } from '../lib/utils'
import {
  Search,
  Filter,
  MapPin,
  Star,
  Users,
  Eye,
  Heart,
  Lock,
  ChevronRight,
  AlertCircle
} from 'lucide-react'
import { toast } from 'sonner'

const PropertiesPage: React.FC = () => {
  const { isAuthenticated, user } = useAuth()
  const { emitPropertyView, emitPropertyFavorite } = useSocket()
  const [searchParams, setSearchParams] = useSearchParams()

  const [properties, setProperties] = useState<Property[]>([])
  const [loading, setLoading] = useState(true)
  const [pagination, setPagination] = useState({
    current_page: 1,
    total_pages: 1,
    total_items: 0,
    has_next: false,
    has_prev: false
  })

  const [filters, setFilters] = useState({
    search: searchParams.get('search') || '',
    city: searchParams.get('city') || '',
    property_type: searchParams.get('property_type') || '',
    min_price: searchParams.get('min_price') || '',
    max_price: searchParams.get('max_price') || '',
    page: parseInt(searchParams.get('page') || '1')
  })

  const [showFilters, setShowFilters] = useState(false)

  useEffect(() => {
    fetchProperties()
  }, [filters.page])

  const fetchProperties = async () => {
    try {
      setLoading(true)
      const response = await propertiesApi.getAll({
        ...filters,
        min_price: filters.min_price ? parseFloat(filters.min_price) : undefined,
        max_price: filters.max_price ? parseFloat(filters.max_price) : undefined
      })

      setProperties(response.data.data.properties || [])
      setPagination(response.data.data.pagination)
    } catch (error: any) {
      toast.error('Gagal memuat data kost')
      console.error('Error fetching properties:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    updateSearchParams({ ...filters, page: 1 })
    fetchProperties()
  }

  const updateSearchParams = (newFilters: typeof filters) => {
    const params = new URLSearchParams()
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value) params.set(key, value.toString())
    })
    setSearchParams(params)
    setFilters(newFilters)
  }

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }))
  }

  const PropertyCard: React.FC<{ property: Property }> = ({ property }) => {
    const [showLoginPrompt, setShowLoginPrompt] = useState(false)

    const handleViewDetails = () => {
      if (!isAuthenticated) {
        setShowLoginPrompt(true)
      }
    }

    const handleFavoriteToggle = async (e: React.MouseEvent) => {
      e.preventDefault()
      e.stopPropagation()

      if (!isAuthenticated) {
        setShowLoginPrompt(true)
        return
      }

      try {
        if (property.is_favorited) {
          await usersApi.removeFavorite(property.id)
          setProperties(prev => prev.map(p =>
            p.id === property.id ? { ...p, is_favorited: false } : p
          ))
          toast.success('Dihapus dari favorit')

          // Emit socket event for real-time notification
          emitPropertyFavorite(property.id, 'remove')
        } else {
          await usersApi.addFavorite(property.id)
          setProperties(prev => prev.map(p =>
            p.id === property.id ? { ...p, is_favorited: true } : p
          ))
          toast.success('Ditambahkan ke favorit')

          // Emit socket event for real-time notification
          emitPropertyFavorite(property.id, 'add')
        }
      } catch (error: any) {
        toast.error('Gagal mengubah status favorit')
      }
    }

    return (
      <div className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow">
        {/* Property Image */}
        <div className="relative">
          <img
            src={property.main_image || 'https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=400&h=300&fit=crop&crop=center'}
            alt={property.name}
            className="w-full h-48 object-cover rounded-t-lg"
          />
          {!isAuthenticated && (
            <div className="absolute top-2 right-2 bg-orange-500 text-white px-2 py-1 rounded text-xs font-medium">
              Preview Terbatas
            </div>
          )}
          <div className="absolute bottom-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
            {property.property_type === 'putra' ? 'Putra' :
             property.property_type === 'putri' ? 'Putri' : 'Campur'}
          </div>
        </div>

        {/* Property Info */}
        <div className="p-4">
          <div className="flex items-start justify-between mb-2">
            <h3 className="text-lg font-semibold text-gray-900 line-clamp-1">
              {property.name}
            </h3>
            <div className="flex items-center text-yellow-500">
              <Star className="h-4 w-4 fill-current" />
              <span className="text-sm text-gray-600 ml-1">
                {property.avg_rating.toFixed(1)} ({property.review_count})
              </span>
            </div>
          </div>

          <div className="flex items-center text-gray-600 mb-2">
            <MapPin className="h-4 w-4 mr-1" />
            <span className="text-sm">{property.address}</span>
          </div>

          <div className="flex items-center text-gray-600 mb-3">
            <Users className="h-4 w-4 mr-1" />
            <span className="text-sm">
              {property.available_rooms} dari {property.total_rooms} kamar tersedia
            </span>
          </div>

          {/* Price Range */}
          <div className="mb-3">
            <span className="text-lg font-bold text-primary">
              {formatPrice(property.min_price)}
              {property.min_price !== property.max_price && (
                <span className="text-gray-500"> - {formatPrice(property.max_price)}</span>
              )}
            </span>
            <span className="text-gray-500 text-sm">/bulan</span>
          </div>

          {/* Facilities Preview */}
          <div className="mb-4">
            <div className="flex flex-wrap gap-1 mb-2">
              {property.facilities.slice(0, isAuthenticated ? property.facilities.length : 3).map((facility, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-700"
                >
                  {facility.name}
                </span>
              ))}
              {!isAuthenticated && property.facilities_limited && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-orange-100 text-orange-700">
                  <Lock className="h-3 w-3 mr-1" />
                  +{property.facilities.length - 3} lainnya
                </span>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            {isAuthenticated ? (
              <Link
                to={`/properties/${property.id}`}
                className="flex-1 bg-primary text-white text-center py-2 px-4 rounded-md hover:bg-primary/90 transition-colors"
              >
                Lihat Detail
              </Link>
            ) : (
              <button
                onClick={handleViewDetails}
                className="flex-1 bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 transition-colors flex items-center justify-center"
              >
                <Eye className="h-4 w-4 mr-2" />
                Lihat Detail
              </button>
            )}

            {isAuthenticated && (
              <button
                onClick={handleFavoriteToggle}
                className={`p-2 border rounded-md transition-colors ${
                  property.is_favorited
                    ? 'border-red-300 bg-red-50 hover:bg-red-100'
                    : 'border-gray-300 hover:bg-gray-50'
                }`}
              >
                <Heart className={`h-4 w-4 ${
                  property.is_favorited ? 'text-red-600 fill-current' : 'text-gray-600'
                }`} />
              </button>
            )}
          </div>
        </div>

        {/* Login Prompt Modal */}
        {showLoginPrompt && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg p-6 max-w-md w-full">
              <div className="text-center">
                <AlertCircle className="h-12 w-12 text-orange-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Daftar untuk Melihat Detail Lengkap
                </h3>
                <p className="text-gray-600 mb-6">
                  Untuk melihat informasi lengkap seperti kontak pemilik, foto detail, dan fasilitas lengkap,
                  silakan daftar atau masuk ke akun Anda.
                </p>
                <div className="flex gap-3">
                  <button
                    onClick={() => setShowLoginPrompt(false)}
                    className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    Tutup
                  </button>
                  <Link
                    to="/register"
                    className="flex-1 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90"
                  >
                    Daftar Gratis
                  </Link>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Cari Kost</h1>

          {/* Search Form */}
          <form onSubmit={handleSearch} className="flex gap-4 mb-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Cari berdasarkan nama kost atau lokasi..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
              />
            </div>
            <button
              type="button"
              onClick={() => setShowFilters(!showFilters)}
              className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 flex items-center"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-primary text-white rounded-md hover:bg-primary/90"
            >
              Cari
            </button>
          </form>

          {/* Filters */}
          {showFilters && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Kota</label>
                <input
                  type="text"
                  placeholder="Contoh: Surakarta"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  value={filters.city}
                  onChange={(e) => handleFilterChange('city', e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Tipe</label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  value={filters.property_type}
                  onChange={(e) => handleFilterChange('property_type', e.target.value)}
                >
                  <option value="">Semua Tipe</option>
                  <option value="putra">Putra</option>
                  <option value="putri">Putri</option>
                  <option value="campur">Campur</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Harga Min</label>
                <input
                  type="number"
                  placeholder="500000"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  value={filters.min_price}
                  onChange={(e) => handleFilterChange('min_price', e.target.value)}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Harga Max</label>
                <input
                  type="number"
                  placeholder="2000000"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  value={filters.max_price}
                  onChange={(e) => handleFilterChange('max_price', e.target.value)}
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 py-8">
        {/* Auth Status Banner */}
        {!isAuthenticated && (
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <AlertCircle className="h-5 w-5 text-orange-500 mr-3" />
                <div>
                  <p className="text-orange-800 font-medium">Preview Terbatas</p>
                  <p className="text-orange-700 text-sm">
                    Anda melihat informasi terbatas. Daftar untuk akses penuh ke detail kost dan kontak pemilik.
                  </p>
                </div>
              </div>
              <Link
                to="/register"
                className="bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 transition-colors flex items-center"
              >
                Daftar Gratis
                <ChevronRight className="h-4 w-4 ml-1" />
              </Link>
            </div>
          </div>
        )}

        {/* Results Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              {pagination.total_items} Kost Ditemukan
            </h2>
            {filters.search && (
              <p className="text-gray-600">
                Hasil pencarian untuk "{filters.search}"
              </p>
            )}
          </div>
        </div>

        {/* Loading State */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-white rounded-lg shadow-sm border animate-pulse">
                <div className="h-48 bg-gray-200 rounded-t-lg"></div>
                <div className="p-4">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded mb-4"></div>
                  <div className="h-8 bg-gray-200 rounded"></div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <>
            {/* Properties Grid */}
            {properties.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                {properties.map((property) => (
                  <PropertyCard key={property.id} property={property} />
                ))}
              </div>
            ) : (
              <div className="text-center py-16">
                <div className="text-gray-400 mb-4">
                  <Search className="h-16 w-16 mx-auto" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Tidak ada kost ditemukan
                </h3>
                <p className="text-gray-600 mb-4">
                  Coba ubah filter pencarian atau kata kunci Anda
                </p>
                <button
                  onClick={() => {
                    setFilters({
                      search: '',
                      city: '',
                      property_type: '',
                      min_price: '',
                      max_price: '',
                      page: 1
                    })
                    setSearchParams(new URLSearchParams())
                    fetchProperties()
                  }}
                  className="btn-primary"
                >
                  Reset Filter
                </button>
              </div>
            )}

            {/* Pagination */}
            {pagination.total_pages > 1 && (
              <div className="flex items-center justify-center space-x-2">
                <button
                  onClick={() => handlePageChange(pagination.current_page - 1)}
                  disabled={!pagination.has_prev}
                  className="px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Sebelumnya
                </button>

                {[...Array(pagination.total_pages)].map((_, index) => {
                  const page = index + 1
                  const isCurrentPage = page === pagination.current_page

                  return (
                    <button
                      key={page}
                      onClick={() => handlePageChange(page)}
                      className={`px-3 py-2 border rounded-md ${
                        isCurrentPage
                          ? 'bg-primary text-white border-primary'
                          : 'border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {page}
                    </button>
                  )
                })}

                <button
                  onClick={() => handlePageChange(pagination.current_page + 1)}
                  disabled={!pagination.has_next}
                  className="px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                >
                  Selanjutnya
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}

export default PropertiesPage
