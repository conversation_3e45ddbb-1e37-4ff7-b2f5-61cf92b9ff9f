import React, { useState, useEffect, useRef } from 'react'
import { useParams, Link, useNavigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import { useSocket } from '../contexts/SocketContext'
import { propertiesApi, usersApi, type Property } from '../lib/api'
import { formatPrice, formatDate } from '../lib/utils'
import {
  MapPin,
  Star,
  Users,
  Phone,
  Mail,
  Heart,
  Share2,
  ArrowLeft,
  Lock,
  AlertCircle,
  ChevronRight,
  Wifi,
  Car,
  Shield,
  Home,
  Bath,
  Bed,
  Desk,
  Wind
} from 'lucide-react'
import { toast } from 'sonner'

const PropertyDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const { isAuthenticated, user } = useAuth()
  const { emitPropertyView, emitPropertyFavorite } = useSocket()
  const navigate = useNavigate()

  const [property, setProperty] = useState<Property | null>(null)
  const [loading, setLoading] = useState(true)
  const [showLoginPrompt, setShowLoginPrompt] = useState(false)
  const [isFavorited, setIsFavorited] = useState(false)
  const [selectedImageIndex, setSelectedImageIndex] = useState(0)
  const isMountedRef = useRef(true)

  useEffect(() => {
    return () => {
      isMountedRef.current = false
    }
  }, [])

  useEffect(() => {
    if (id) {
      fetchPropertyDetail()
    }
  }, [id, isAuthenticated])

  useEffect(() => {
    // Emit property view event when authenticated user views the property
    if (property && isAuthenticated && user) {
      emitPropertyView(property.id)
    }
  }, [property, isAuthenticated, user, emitPropertyView])

  const fetchPropertyDetail = async () => {
    try {
      setLoading(true)
      const response = await propertiesApi.getById(id!)

      // Check if component is still mounted before updating state
      if (isMountedRef.current) {
        setProperty(response.data.data.property)
        setIsFavorited(response.data.data.property.is_favorited || false)
      }
    } catch (error: any) {
      if (isMountedRef.current) {
        toast.error('Gagal memuat detail kost')
        console.error('Error fetching property detail:', error)
      }
    } finally {
      if (isMountedRef.current) {
        setLoading(false)
      }
    }
  }

  const handleFavoriteToggle = async () => {
    if (!isAuthenticated) {
      setShowLoginPrompt(true)
      return
    }

    try {
      if (isFavorited) {
        await usersApi.removeFavorite(id!)
        setIsFavorited(false)
        toast.success('Dihapus dari favorit')

        // Emit socket event for real-time notification
        emitPropertyFavorite(id!, 'remove')
      } else {
        await usersApi.addFavorite(id!)
        setIsFavorited(true)
        toast.success('Ditambahkan ke favorit')

        // Emit socket event for real-time notification
        emitPropertyFavorite(id!, 'add')
      }
    } catch (error: any) {
      toast.error('Gagal mengubah status favorit')
    }
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: property?.name,
          text: `Lihat kost ${property?.name} di KOST2`,
          url: window.location.href,
        })
      } catch (error) {
        // User cancelled sharing
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
      toast.success('Link disalin ke clipboard')
    }
  }

  const getFacilityIcon = (facilityName: string) => {
    const name = facilityName.toLowerCase()
    if (name.includes('wifi')) return <Wifi className="h-4 w-4" />
    if (name.includes('ac')) return <Wind className="h-4 w-4" />
    if (name.includes('parkir') || name.includes('motor') || name.includes('mobil')) return <Car className="h-4 w-4" />
    if (name.includes('cctv') || name.includes('satpam') || name.includes('keamanan')) return <Shield className="h-4 w-4" />
    if (name.includes('kamar mandi') || name.includes('bath')) return <Bath className="h-4 w-4" />
    if (name.includes('kasur') || name.includes('bed')) return <Bed className="h-4 w-4" />
    if (name.includes('meja') || name.includes('desk')) return <Desk className="h-4 w-4" />
    return <Home className="h-4 w-4" />
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (!property) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">404</h1>
        <p className="text-gray-600 mb-8">Kost yang Anda cari tidak ditemukan.</p>
        <Link to="/properties" className="btn-primary">
          Kembali ke Pencarian
        </Link>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <button
              onClick={() => navigate(-1)}
              className="flex items-center text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Kembali
            </button>
            <div className="flex items-center space-x-2">
              <button
                onClick={handleShare}
                className="p-2 border border-gray-300 rounded-md hover:bg-gray-50"
              >
                <Share2 className="h-4 w-4" />
              </button>
              <button
                onClick={handleFavoriteToggle}
                className={`p-2 border rounded-md transition-colors ${
                  isFavorited
                    ? 'bg-red-50 border-red-200 text-red-600'
                    : 'border-gray-300 hover:bg-gray-50'
                }`}
              >
                <Heart className={`h-4 w-4 ${isFavorited ? 'fill-current' : ''}`} />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Auth Status Banner */}
      {!isAuthenticated && (
        <div className="bg-orange-50 border-b border-orange-200">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <AlertCircle className="h-5 w-5 text-orange-500 mr-3" />
                <div>
                  <p className="text-orange-800 font-medium">Informasi Terbatas</p>
                  <p className="text-orange-700 text-sm">
                    Daftar untuk melihat kontak pemilik, foto lengkap, dan detail fasilitas.
                  </p>
                </div>
              </div>
              <Link
                to="/register"
                className="bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 transition-colors flex items-center"
              >
                Daftar Gratis
                <ChevronRight className="h-4 w-4 ml-1" />
              </Link>
            </div>
          </div>
        </div>
      )}

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Property Images */}
            <div className="bg-white rounded-lg shadow-sm mb-6">
              <div className="relative">
                <img
                  src={property.images?.[selectedImageIndex]?.image_url || property.main_image || 'https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800&h=600&fit=crop&crop=center'}
                  alt={property.name}
                  className="w-full h-96 object-cover rounded-t-lg"
                />
                {!isAuthenticated && (
                  <div className="absolute inset-0 bg-black/20 flex items-center justify-center rounded-t-lg">
                    <div className="bg-white/90 backdrop-blur-sm px-4 py-2 rounded-lg text-center">
                      <Lock className="h-6 w-6 text-orange-500 mx-auto mb-2" />
                      <p className="text-sm font-medium text-gray-900">
                        Daftar untuk melihat {property.images?.length || 0} foto lengkap
                      </p>
                    </div>
                  </div>
                )}
              </div>

              {/* Image Thumbnails */}
              {isAuthenticated && property.images && property.images.length > 1 && (
                <div className="p-4 border-t">
                  <div className="flex space-x-2 overflow-x-auto">
                    {property.images.map((image, index) => (
                      <button
                        key={index}
                        onClick={() => setSelectedImageIndex(index)}
                        className={`flex-shrink-0 w-20 h-20 rounded-md overflow-hidden border-2 ${
                          selectedImageIndex === index ? 'border-primary' : 'border-gray-200'
                        }`}
                      >
                        <img
                          src={image.image_url}
                          alt={`${property.name} ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Property Info */}
            <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">{property.name}</h1>
                  <div className="flex items-center text-gray-600 mb-2">
                    <MapPin className="h-4 w-4 mr-1" />
                    <span>{property.address}</span>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center">
                      <Star className="h-4 w-4 text-yellow-500 fill-current mr-1" />
                      <span className="font-medium">{property.avg_rating.toFixed(1)}</span>
                      <span className="text-gray-500 ml-1">({property.review_count} ulasan)</span>
                    </div>
                    <div className="flex items-center text-gray-600">
                      <Users className="h-4 w-4 mr-1" />
                      <span>{property.available_rooms} dari {property.total_rooms} kamar tersedia</span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-primary">
                    {formatPrice(property.min_price)}
                    {property.min_price !== property.max_price && (
                      <span className="text-lg text-gray-500"> - {formatPrice(property.max_price)}</span>
                    )}
                  </div>
                  <div className="text-gray-500">per bulan</div>
                  <div className="mt-2">
                    <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
                      property.property_type === 'putra' ? 'bg-blue-100 text-blue-800' :
                      property.property_type === 'putri' ? 'bg-pink-100 text-pink-800' :
                      'bg-purple-100 text-purple-800'
                    }`}>
                      {property.property_type === 'putra' ? 'Khusus Putra' :
                       property.property_type === 'putri' ? 'Khusus Putri' : 'Putra & Putri'}
                    </span>
                  </div>
                </div>
              </div>

              <div className="border-t pt-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Deskripsi</h3>
                <p className="text-gray-600 leading-relaxed">{property.description}</p>
              </div>
            </div>

            {/* Facilities */}
            <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Fasilitas</h3>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {(isAuthenticated ? property.facilities : property.facilities.slice(0, 6)).map((facility, index) => (
                  <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <div className="text-primary">
                      {getFacilityIcon(facility.name)}
                    </div>
                    <span className="text-gray-700">{facility.name}</span>
                  </div>
                ))}
                {!isAuthenticated && property.facilities.length > 6 && (
                  <div className="flex items-center space-x-3 p-3 bg-orange-50 rounded-lg border border-orange-200">
                    <Lock className="h-4 w-4 text-orange-500" />
                    <span className="text-orange-700 font-medium">
                      +{property.facilities.length - 6} fasilitas lainnya
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Rooms */}
            {isAuthenticated && property.rooms && property.rooms.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Daftar Kamar</h3>
                <div className="space-y-4">
                  {property.rooms.map((room, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div>
                          <h4 className="font-medium text-gray-900">
                            Kamar {room.room_number} - {room.room_type}
                          </h4>
                          <p className="text-sm text-gray-600">{room.size_sqm} m²</p>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold text-primary">
                            {formatPrice(room.price_monthly)}
                          </div>
                          <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
                            room.status === 'available' ? 'bg-green-100 text-green-800' :
                            room.status === 'occupied' ? 'bg-red-100 text-red-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {room.status === 'available' ? 'Tersedia' :
                             room.status === 'occupied' ? 'Terisi' : 'Maintenance'}
                          </span>
                        </div>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {room.has_ac && <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">AC</span>}
                        {room.has_wifi && <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">WiFi</span>}
                        {room.has_private_bathroom && <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Kamar Mandi Dalam</span>}
                        {room.has_wardrobe && <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Lemari</span>}
                        {room.has_desk && <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Meja Belajar</span>}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Testimonials */}
            {isAuthenticated && property.testimonials && property.testimonials.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Ulasan Penghuni</h3>
                <div className="space-y-4">
                  {property.testimonials.map((testimonial, index) => (
                    <div key={index} className="border-b border-gray-200 pb-4 last:border-b-0">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-medium mr-3">
                            {testimonial.user_name.charAt(0).toUpperCase()}
                          </div>
                          <div>
                            <p className="font-medium text-gray-900">{testimonial.user_name}</p>
                            <p className="text-sm text-gray-500">{formatDate(testimonial.created_at)}</p>
                          </div>
                        </div>
                        <div className="flex items-center">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`h-4 w-4 ${
                                i < testimonial.rating ? 'text-yellow-500 fill-current' : 'text-gray-300'
                              }`}
                            />
                          ))}
                        </div>
                      </div>
                      <p className="text-gray-600">{testimonial.comment}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            {/* Contact Card */}
            <div className="bg-white rounded-lg shadow-sm p-6 mb-6 sticky top-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Kontak Pemilik</h3>

              {isAuthenticated ? (
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-primary text-white rounded-full flex items-center justify-center text-lg font-medium">
                      {property.owner_name?.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{property.owner_name}</p>
                      <p className="text-sm text-gray-600">Pemilik Kost</p>
                    </div>
                  </div>

                  {property.owner_phone && (
                    <a
                      href={`tel:${property.owner_phone}`}
                      className="flex items-center justify-center w-full bg-green-500 text-white py-3 px-4 rounded-md hover:bg-green-600 transition-colors"
                    >
                      <Phone className="h-4 w-4 mr-2" />
                      Hubungi via WhatsApp
                    </a>
                  )}

                  {property.owner_email && (
                    <a
                      href={`mailto:${property.owner_email}`}
                      className="flex items-center justify-center w-full border border-gray-300 text-gray-700 py-3 px-4 rounded-md hover:bg-gray-50 transition-colors"
                    >
                      <Mail className="h-4 w-4 mr-2" />
                      Kirim Email
                    </a>
                  )}
                </div>
              ) : (
                <div className="text-center">
                  <Lock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 mb-4">
                    Informasi kontak hanya tersedia untuk pengguna terdaftar
                  </p>
                  <Link
                    to="/register"
                    className="block w-full bg-primary text-white py-3 px-4 rounded-md hover:bg-primary/90 transition-colors text-center"
                  >
                    Daftar untuk Melihat Kontak
                  </Link>
                </div>
              )}
            </div>

            {/* Quick Info */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Informasi Cepat</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Tipe Kost</span>
                  <span className="font-medium">
                    {property.property_type === 'putra' ? 'Khusus Putra' :
                     property.property_type === 'putri' ? 'Khusus Putri' : 'Putra & Putri'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Kamar</span>
                  <span className="font-medium">{property.total_rooms} kamar</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Kamar Tersedia</span>
                  <span className="font-medium text-green-600">{property.available_rooms} kamar</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Rating</span>
                  <div className="flex items-center">
                    <Star className="h-4 w-4 text-yellow-500 fill-current mr-1" />
                    <span className="font-medium">{property.avg_rating.toFixed(1)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Login Prompt Modal */}
      {showLoginPrompt && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-orange-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Daftar untuk Akses Penuh
              </h3>
              <p className="text-gray-600 mb-6">
                Untuk menambahkan ke favorit dan mengakses fitur lengkap lainnya,
                silakan daftar atau masuk ke akun Anda.
              </p>
              <div className="flex gap-3">
                <button
                  onClick={() => setShowLoginPrompt(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Tutup
                </button>
                <Link
                  to="/register"
                  className="flex-1 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90"
                >
                  Daftar Gratis
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default PropertyDetailPage
