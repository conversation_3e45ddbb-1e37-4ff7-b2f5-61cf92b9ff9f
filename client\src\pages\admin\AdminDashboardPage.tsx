import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import { adminApi } from '../../lib/api'
import { formatDate } from '../../lib/utils'
import { 
  Users, 
  Building2, 
  Eye, 
  Heart, 
  TrendingUp,
  TrendingDown,
  UserCheck,
  UserX,
  Shield,
  BarChart3,
  Settings,
  AlertTriangle,
  Activity,
  Calendar
} from 'lucide-react'
import { toast } from 'sonner'
import LoadingSpinner from '../../components/ui/LoadingSpinner'

interface AdminStats {
  users: {
    total: number
    by_role: Record<string, number>
    monthly_registrations: number
  }
  properties: {
    total: number
    by_status: Record<string, number>
    monthly_additions: number
  }
  engagement: {
    total_views: number
    total_favorites: number
  }
  activity: Array<{ date: string; registrations: number }>
}

const AdminDashboardPage: React.FC = () => {
  const { user } = useAuth()
  
  const [stats, setStats] = useState<AdminStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardStats()
  }, [])

  const fetchDashboardStats = async () => {
    try {
      setLoading(true)
      const response = await adminApi.getDashboardStats()
      setStats(response.data.data)
    } catch (error: any) {
      console.error('Error fetching admin stats:', error)
      toast.error('Gagal memuat statistik admin')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="xl" />
      </div>
    )
  }

  if (!stats) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">Error</h1>
        <p className="text-gray-600 mb-8">Gagal memuat data dashboard admin.</p>
        <button onClick={fetchDashboardStats} className="btn-primary">
          Coba Lagi
        </button>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Dashboard Admin
          </h1>
          <p className="text-gray-600">
            Kelola sistem KOST2 dan pantau aktivitas platform
          </p>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-3 bg-blue-100 rounded-lg">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Pengguna</p>
                <p className="text-2xl font-bold text-gray-900">{stats.users.total}</p>
                <p className="text-xs text-gray-500">
                  +{stats.users.monthly_registrations} bulan ini
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-3 bg-green-100 rounded-lg">
                <Building2 className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Properti</p>
                <p className="text-2xl font-bold text-gray-900">{stats.properties.total}</p>
                <p className="text-xs text-gray-500">
                  +{stats.properties.monthly_additions} bulan ini
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-3 bg-purple-100 rounded-lg">
                <Eye className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Views</p>
                <p className="text-2xl font-bold text-gray-900">{stats.engagement.total_views}</p>
                <p className="text-xs text-gray-500">Semua properti</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-3 bg-red-100 rounded-lg">
                <Heart className="h-6 w-6 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Favorit</p>
                <p className="text-2xl font-bold text-gray-900">{stats.engagement.total_favorites}</p>
                <p className="text-xs text-gray-500">Semua properti</p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Quick Actions */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Aksi Cepat</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Link
                  to="/admin/users"
                  className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="p-2 bg-blue-100 rounded-lg mr-4">
                    <Users className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">Kelola Pengguna</h3>
                    <p className="text-sm text-gray-600">Lihat dan kelola semua pengguna</p>
                  </div>
                </Link>

                <Link
                  to="/admin/properties"
                  className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="p-2 bg-green-100 rounded-lg mr-4">
                    <Building2 className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">Kelola Properti</h3>
                    <p className="text-sm text-gray-600">Moderasi dan kelola properti</p>
                  </div>
                </Link>

                <Link
                  to="/admin/analytics"
                  className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="p-2 bg-purple-100 rounded-lg mr-4">
                    <BarChart3 className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">Analitik Sistem</h3>
                    <p className="text-sm text-gray-600">Lihat statistik dan tren</p>
                  </div>
                </Link>

                <Link
                  to="/admin/settings"
                  className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="p-2 bg-gray-100 rounded-lg mr-4">
                    <Settings className="h-5 w-5 text-gray-600" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">Pengaturan</h3>
                    <p className="text-sm text-gray-600">Konfigurasi sistem</p>
                  </div>
                </Link>
              </div>
            </div>

            {/* User Distribution */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Distribusi Pengguna</h3>
              <div className="space-y-4">
                {Object.entries(stats.users.by_role).map(([role, count]) => (
                  <div key={role} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className={`p-2 rounded-lg mr-3 ${
                        role === 'admin' ? 'bg-red-100' :
                        role === 'owner' ? 'bg-blue-100' : 'bg-green-100'
                      }`}>
                        {role === 'admin' ? (
                          <Shield className={`h-4 w-4 ${
                            role === 'admin' ? 'text-red-600' :
                            role === 'owner' ? 'text-blue-600' : 'text-green-600'
                          }`} />
                        ) : (
                          <Users className={`h-4 w-4 ${
                            role === 'admin' ? 'text-red-600' :
                            role === 'owner' ? 'text-blue-600' : 'text-green-600'
                          }`} />
                        )}
                      </div>
                      <span className="font-medium text-gray-900 capitalize">{role}</span>
                    </div>
                    <div className="flex items-center">
                      <span className="text-lg font-bold text-gray-900 mr-2">{count}</span>
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${
                            role === 'admin' ? 'bg-red-500' :
                            role === 'owner' ? 'bg-blue-500' : 'bg-green-500'
                          }`}
                          style={{ width: `${(count / stats.users.total) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Property Status */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Status Properti</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {stats.properties.by_status.available || 0}
                  </div>
                  <div className="text-sm text-green-700">Tersedia</div>
                </div>
                <div className="text-center p-4 bg-red-50 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">
                    {stats.properties.by_status.full || 0}
                  </div>
                  <div className="text-sm text-red-700">Penuh</div>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Admin Profile */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center space-x-4 mb-4">
                <div className="w-12 h-12 bg-red-500 text-white rounded-full flex items-center justify-center text-lg font-medium">
                  {user?.full_name?.charAt(0).toUpperCase()}
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">{user?.full_name}</h3>
                  <p className="text-sm text-gray-600">Administrator</p>
                </div>
              </div>
              <Link
                to="/profile"
                className="block w-full text-center py-2 px-4 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Edit Profil
              </Link>
            </div>

            {/* Recent Activity */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-medium text-gray-900">Aktivitas Terbaru</h3>
                <Activity className="h-4 w-4 text-gray-400" />
              </div>
              
              <div className="space-y-3">
                {stats.activity.slice(-5).map((item, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {item.registrations} registrasi baru
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatDate(item.date)}
                      </p>
                    </div>
                    <div className="flex items-center">
                      {item.registrations > 0 ? (
                        <TrendingUp className="h-4 w-4 text-green-500" />
                      ) : (
                        <TrendingDown className="h-4 w-4 text-gray-400" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* System Status */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="font-medium text-gray-900 mb-4">Status Sistem</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Server Status</span>
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Online
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Database</span>
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Connected
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">Storage</span>
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    75% Used
                  </span>
                </div>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg p-6 text-white">
              <h3 className="font-medium mb-4">Platform Growth</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-blue-100">Pengguna Baru</span>
                  <span className="font-bold">+{stats.users.monthly_registrations}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-blue-100">Properti Baru</span>
                  <span className="font-bold">+{stats.properties.monthly_additions}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-blue-100">Growth Rate</span>
                  <span className="font-bold">
                    {stats.users.total > 0 ? ((stats.users.monthly_registrations / stats.users.total) * 100).toFixed(1) : 0}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AdminDashboardPage
