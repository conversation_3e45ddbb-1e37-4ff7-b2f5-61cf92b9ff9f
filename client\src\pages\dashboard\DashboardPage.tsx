import React, { useState, useEffect } from 'react'
import { Link, Navigate } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import { usersApi, propertiesApi, type Property, type Notification } from '../../lib/api'
import { formatPrice, formatRelativeTime } from '../../lib/utils'
import {
  Heart,
  Search,
  Bell,
  Star,
  MapPin,
  TrendingUp,
  Eye,
  Calendar,
  Users,
  Building2,
  ChevronRight,
  Plus
} from 'lucide-react'
import { toast } from 'sonner'
import LoadingSpinner from '../../components/ui/LoadingSpinner'

const DashboardPage: React.FC = () => {
  const { user } = useAuth()

  // Redirect owners to their specific dashboard
  if (user?.role === 'owner') {
    return <Navigate to="/owner/dashboard" replace />
  }

  const [favorites, setFavorites] = useState<Property[]>([])
  const [recentProperties, setRecentProperties] = useState<Property[]>([])
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    totalFavorites: 0,
    totalViews: 0,
    savedSearches: 0
  })

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)

      // Fetch favorites
      const favoritesResponse = await usersApi.getFavorites()
      setFavorites(favoritesResponse.data.data.favorites.slice(0, 3)) // Show only 3 recent
      setStats(prev => ({ ...prev, totalFavorites: favoritesResponse.data.data.favorites.length }))

      // Fetch recent properties (simulated - in real app this would be from user's view history)
      const propertiesResponse = await propertiesApi.getAll({ limit: 3 })
      setRecentProperties(propertiesResponse.data.data.properties || [])

      // Fetch notifications
      const notificationsResponse = await usersApi.getNotifications({ limit: 5 })
      setNotifications(notificationsResponse.data.data.notifications)
      setUnreadCount(notificationsResponse.data.data.unread_count)

    } catch (error: any) {
      console.error('Error fetching dashboard data:', error)
      toast.error('Gagal memuat data dashboard')
    } finally {
      setLoading(false)
    }
  }

  const handleRemoveFavorite = async (propertyId: string) => {
    try {
      await usersApi.removeFavorite(propertyId)
      setFavorites(prev => prev.filter(p => p.id !== propertyId))
      setStats(prev => ({ ...prev, totalFavorites: prev.totalFavorites - 1 }))
      toast.success('Dihapus dari favorit')
    } catch (error: any) {
      toast.error('Gagal menghapus dari favorit')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="xl" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Selamat datang, {user?.full_name}!
          </h1>
          <p className="text-gray-600">
            Kelola pencarian kost dan favorit Anda dengan mudah
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-3 bg-red-100 rounded-lg">
                <Heart className="h-6 w-6 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Kost Favorit</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalFavorites}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-3 bg-blue-100 rounded-lg">
                <Eye className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Kost Dilihat</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalViews}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-3 bg-green-100 rounded-lg">
                <Search className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pencarian Tersimpan</p>
                <p className="text-2xl font-bold text-gray-900">{stats.savedSearches}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Quick Actions */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Aksi Cepat</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Link
                  to="/properties"
                  className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="p-2 bg-primary/10 rounded-lg mr-4">
                    <Search className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">Cari Kost</h3>
                    <p className="text-sm text-gray-600">Temukan kost impian Anda</p>
                  </div>
                  <ChevronRight className="h-5 w-5 text-gray-400 ml-auto" />
                </Link>

                <Link
                  to="/favorites"
                  className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="p-2 bg-red-100 rounded-lg mr-4">
                    <Heart className="h-5 w-5 text-red-600" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">Lihat Favorit</h3>
                    <p className="text-sm text-gray-600">Kost yang Anda sukai</p>
                  </div>
                  <ChevronRight className="h-5 w-5 text-gray-400 ml-auto" />
                </Link>
              </div>
            </div>

            {/* Recent Favorites */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900">Favorit Terbaru</h2>
                <Link
                  to="/favorites"
                  className="text-primary hover:text-primary/80 text-sm font-medium"
                >
                  Lihat Semua
                </Link>
              </div>

              {favorites.length > 0 ? (
                <div className="space-y-4">
                  {favorites.map((property) => (
                    <div key={property.id} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                      <img
                        src={property.main_image || 'https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=100&h=100&fit=crop&crop=center'}
                        alt={property.name}
                        className="w-16 h-16 object-cover rounded-lg"
                      />
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900">{property.name}</h3>
                        <div className="flex items-center text-gray-600 text-sm">
                          <MapPin className="h-3 w-3 mr-1" />
                          <span>{property.city}</span>
                        </div>
                        <div className="flex items-center space-x-4 mt-1">
                          <span className="text-primary font-medium">
                            {formatPrice(property.min_price)}/bulan
                          </span>
                          <div className="flex items-center text-yellow-500">
                            <Star className="h-3 w-3 fill-current mr-1" />
                            <span className="text-xs">{property.avg_rating.toFixed(1)}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Link
                          to={`/properties/${property.id}`}
                          className="text-primary hover:text-primary/80 text-sm font-medium"
                        >
                          Lihat
                        </Link>
                        <button
                          onClick={() => handleRemoveFavorite(property.id)}
                          className="text-red-600 hover:text-red-700 text-sm"
                        >
                          Hapus
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Heart className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-600 mb-4">Belum ada kost favorit</p>
                  <Link to="/properties" className="btn-primary">
                    Mulai Mencari Kost
                  </Link>
                </div>
              )}
            </div>

            {/* Recommended Properties */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900">Rekomendasi untuk Anda</h2>
                <Link
                  to="/properties"
                  className="text-primary hover:text-primary/80 text-sm font-medium"
                >
                  Lihat Semua
                </Link>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {recentProperties.slice(0, 2).map((property) => (
                  <Link
                    key={property.id}
                    to={`/properties/${property.id}`}
                    className="block border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
                  >
                    <img
                      src={property.main_image || 'https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=300&h=200&fit=crop&crop=center'}
                      alt={property.name}
                      className="w-full h-32 object-cover"
                    />
                    <div className="p-4">
                      <h3 className="font-medium text-gray-900 mb-1">{property.name}</h3>
                      <div className="flex items-center text-gray-600 text-sm mb-2">
                        <MapPin className="h-3 w-3 mr-1" />
                        <span>{property.city}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-primary font-medium">
                          {formatPrice(property.min_price)}
                        </span>
                        <div className="flex items-center text-yellow-500">
                          <Star className="h-3 w-3 fill-current mr-1" />
                          <span className="text-xs">{property.avg_rating.toFixed(1)}</span>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Profile Summary */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center space-x-4 mb-4">
                <div className="w-12 h-12 bg-primary text-white rounded-full flex items-center justify-center text-lg font-medium">
                  {user?.full_name?.charAt(0).toUpperCase()}
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">{user?.full_name}</h3>
                  <p className="text-sm text-gray-600 capitalize">{user?.role}</p>
                </div>
              </div>
              <Link
                to="/profile"
                className="block w-full text-center py-2 px-4 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Edit Profil
              </Link>
            </div>

            {/* Recent Notifications */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-medium text-gray-900">Notifikasi</h3>
                {unreadCount > 0 && (
                  <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                    {unreadCount}
                  </span>
                )}
              </div>

              {notifications.length > 0 ? (
                <div className="space-y-3">
                  {notifications.slice(0, 3).map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-3 rounded-lg border ${
                        notification.is_read ? 'bg-gray-50' : 'bg-blue-50 border-blue-200'
                      }`}
                    >
                      <p className="text-sm font-medium text-gray-900">
                        {notification.title}
                      </p>
                      <p className="text-xs text-gray-600 mt-1">
                        {notification.message}
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        {formatRelativeTime(notification.created_at)}
                      </p>
                    </div>
                  ))}
                  <Link
                    to="/notifications"
                    className="block text-center text-primary hover:text-primary/80 text-sm font-medium pt-2"
                  >
                    Lihat Semua Notifikasi
                  </Link>
                </div>
              ) : (
                <div className="text-center py-4">
                  <Bell className="h-8 w-8 text-gray-300 mx-auto mb-2" />
                  <p className="text-sm text-gray-600">Tidak ada notifikasi</p>
                </div>
              )}
            </div>

            {/* Quick Tips */}
            <div className="bg-gradient-to-br from-primary/10 to-primary/5 rounded-lg p-6">
              <h3 className="font-medium text-gray-900 mb-3">💡 Tips Pencarian</h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Gunakan filter untuk mempersempit pencarian</li>
                <li>• Simpan kost favorit untuk perbandingan</li>
                <li>• Hubungi pemilik langsung untuk info terbaru</li>
                <li>• Baca ulasan dari penghuni sebelumnya</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DashboardPage
