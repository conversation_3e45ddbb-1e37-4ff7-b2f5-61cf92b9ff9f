import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { usersApi, type Property } from '../../lib/api'
import { formatPrice, formatRelativeTime } from '../../lib/utils'
import {
  Heart,
  MapPin,
  Star,
  Users,
  Eye,
  Share2,
  Trash2,
  Compare,
  Filter,
  SortAsc,
  Grid,
  List
} from 'lucide-react'
import { toast } from 'sonner'
import LoadingSpinner from '../../components/ui/LoadingSpinner'

const FavoritesPage: React.FC = () => {
  const [favorites, setFavorites] = useState<Property[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [sortBy, setSortBy] = useState<'date' | 'price' | 'rating'>('date')
  const [filterType, setFilterType] = useState<'all' | 'putra' | 'putri' | 'campur'>('all')

  useEffect(() => {
    fetchFavorites()
  }, [])

  const fetchFavorites = async () => {
    try {
      setLoading(true)
      const response = await usersApi.getFavorites()
      setFavorites(response.data.data.favorites)
    } catch (error: any) {
      toast.error('Gagal memuat daftar favorit')
      console.error('Error fetching favorites:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleRemoveFavorite = async (propertyId: string) => {
    try {
      await usersApi.removeFavorite(propertyId)
      setFavorites(prev => prev.filter(p => p.id !== propertyId))
      setSelectedItems(prev => prev.filter(id => id !== propertyId))
      toast.success('Dihapus dari favorit')
    } catch (error: any) {
      toast.error('Gagal menghapus dari favorit')
    }
  }

  const handleBulkRemove = async () => {
    if (selectedItems.length === 0) return

    try {
      await Promise.all(selectedItems.map(id => usersApi.removeFavorite(id)))
      setFavorites(prev => prev.filter(p => !selectedItems.includes(p.id)))
      setSelectedItems([])
      toast.success(`${selectedItems.length} kost dihapus dari favorit`)
    } catch (error: any) {
      toast.error('Gagal menghapus beberapa kost dari favorit')
    }
  }

  const handleSelectItem = (propertyId: string) => {
    setSelectedItems(prev =>
      prev.includes(propertyId)
        ? prev.filter(id => id !== propertyId)
        : [...prev, propertyId]
    )
  }

  const handleSelectAll = () => {
    if (selectedItems.length === filteredFavorites.length) {
      setSelectedItems([])
    } else {
      setSelectedItems(filteredFavorites.map(p => p.id))
    }
  }

  const handleShare = async (property: Property) => {
    const shareData = {
      title: property.name,
      text: `Lihat kost ${property.name} di KOST2`,
      url: `${window.location.origin}/properties/${property.id}`,
    }

    if (navigator.share) {
      try {
        await navigator.share(shareData)
      } catch (error) {
        // User cancelled sharing
      }
    } else {
      navigator.clipboard.writeText(shareData.url)
      toast.success('Link disalin ke clipboard')
    }
  }

  // Filter and sort favorites
  const filteredFavorites = favorites
    .filter(property => filterType === 'all' || property.property_type === filterType)
    .sort((a, b) => {
      switch (sortBy) {
        case 'price':
          return a.min_price - b.min_price
        case 'rating':
          return b.avg_rating - a.avg_rating
        case 'date':
        default:
          return new Date(b.favorited_at || 0).getTime() - new Date(a.favorited_at || 0).getTime()
      }
    })

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="xl" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Kost Favorit</h1>
            <p className="text-gray-600">
              {favorites.length} kost tersimpan dalam favorit Anda
            </p>
          </div>

          {selectedItems.length > 0 && (
            <div className="flex items-center space-x-3">
              <span className="text-sm text-gray-600">
                {selectedItems.length} dipilih
              </span>
              <button
                onClick={handleBulkRemove}
                className="flex items-center px-3 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Hapus
              </button>
            </div>
          )}
        </div>

        {favorites.length === 0 ? (
          <div className="text-center py-16">
            <Heart className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-2">
              Belum ada kost favorit
            </h3>
            <p className="text-gray-600 mb-6">
              Mulai mencari dan simpan kost yang Anda sukai untuk mempermudah perbandingan
            </p>
            <Link to="/properties" className="btn-primary">
              Mulai Mencari Kost
            </Link>
          </div>
        ) : (
          <>
            {/* Controls */}
            <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                <div className="flex items-center space-x-4">
                  {/* Select All */}
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedItems.length === filteredFavorites.length && filteredFavorites.length > 0}
                      onChange={handleSelectAll}
                      className="rounded border-gray-300 text-primary focus:ring-primary"
                    />
                    <span className="ml-2 text-sm text-gray-700">Pilih Semua</span>
                  </label>

                  {/* Filter */}
                  <select
                    value={filterType}
                    onChange={(e) => setFilterType(e.target.value as any)}
                    className="text-sm border border-gray-300 rounded-md px-3 py-1"
                  >
                    <option value="all">Semua Tipe</option>
                    <option value="putra">Putra</option>
                    <option value="putri">Putri</option>
                    <option value="campur">Campur</option>
                  </select>

                  {/* Sort */}
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as any)}
                    className="text-sm border border-gray-300 rounded-md px-3 py-1"
                  >
                    <option value="date">Terbaru Ditambahkan</option>
                    <option value="price">Harga Terendah</option>
                    <option value="rating">Rating Tertinggi</option>
                  </select>
                </div>

                <div className="flex items-center space-x-2">
                  {/* View Mode Toggle */}
                  <div className="flex border border-gray-300 rounded-md">
                    <button
                      onClick={() => setViewMode('grid')}
                      className={`p-2 ${viewMode === 'grid' ? 'bg-primary text-white' : 'text-gray-600 hover:bg-gray-50'}`}
                    >
                      <Grid className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`p-2 ${viewMode === 'list' ? 'bg-primary text-white' : 'text-gray-600 hover:bg-gray-50'}`}
                    >
                      <List className="h-4 w-4" />
                    </button>
                  </div>

                  {/* Compare Button */}
                  {selectedItems.length >= 2 && (
                    <button className="flex items-center px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors">
                      <Compare className="h-4 w-4 mr-2" />
                      Bandingkan ({selectedItems.length})
                    </button>
                  )}
                </div>
              </div>
            </div>

            {/* Favorites Grid/List */}
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredFavorites.map((property) => (
                  <div key={property.id} className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow">
                    {/* Selection Checkbox */}
                    <div className="relative">
                      <img
                        src={property.main_image || 'https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=400&h=300&fit=crop&crop=center'}
                        alt={property.name}
                        className="w-full h-48 object-cover rounded-t-lg"
                      />
                      <div className="absolute top-2 left-2">
                        <input
                          type="checkbox"
                          checked={selectedItems.includes(property.id)}
                          onChange={() => handleSelectItem(property.id)}
                          className="rounded border-gray-300 text-primary focus:ring-primary"
                        />
                      </div>
                      <div className="absolute bottom-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
                        {property.property_type === 'putra' ? 'Putra' :
                         property.property_type === 'putri' ? 'Putri' : 'Campur'}
                      </div>
                    </div>

                    <div className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <h3 className="text-lg font-semibold text-gray-900 line-clamp-1">
                          {property.name}
                        </h3>
                        <div className="flex items-center text-yellow-500">
                          <Star className="h-4 w-4 fill-current" />
                          <span className="text-sm text-gray-600 ml-1">
                            {property.avg_rating.toFixed(1)}
                          </span>
                        </div>
                      </div>

                      <div className="flex items-center text-gray-600 mb-2">
                        <MapPin className="h-4 w-4 mr-1" />
                        <span className="text-sm">{property.city}</span>
                      </div>

                      <div className="flex items-center text-gray-600 mb-3">
                        <Users className="h-4 w-4 mr-1" />
                        <span className="text-sm">
                          {property.available_rooms} kamar tersedia
                        </span>
                      </div>

                      <div className="mb-4">
                        <span className="text-lg font-bold text-primary">
                          {formatPrice(property.min_price)}
                          {property.min_price !== property.max_price && (
                            <span className="text-gray-500"> - {formatPrice(property.max_price)}</span>
                          )}
                        </span>
                        <span className="text-gray-500 text-sm">/bulan</span>
                      </div>

                      {property.favorited_at && (
                        <p className="text-xs text-gray-500 mb-4">
                          Ditambahkan {formatRelativeTime(property.favorited_at)}
                        </p>
                      )}

                      <div className="flex gap-2">
                        <Link
                          to={`/properties/${property.id}`}
                          className="flex-1 bg-primary text-white text-center py-2 px-4 rounded-md hover:bg-primary/90 transition-colors"
                        >
                          Lihat Detail
                        </Link>
                        <button
                          onClick={() => handleShare(property)}
                          className="p-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                        >
                          <Share2 className="h-4 w-4 text-gray-600" />
                        </button>
                        <button
                          onClick={() => handleRemoveFavorite(property.id)}
                          className="p-2 border border-red-300 rounded-md hover:bg-red-50 transition-colors"
                        >
                          <Heart className="h-4 w-4 text-red-600 fill-current" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {filteredFavorites.map((property) => (
                  <div key={property.id} className="bg-white rounded-lg shadow-sm border p-6">
                    <div className="flex items-start space-x-4">
                      <input
                        type="checkbox"
                        checked={selectedItems.includes(property.id)}
                        onChange={() => handleSelectItem(property.id)}
                        className="mt-1 rounded border-gray-300 text-primary focus:ring-primary"
                      />

                      <img
                        src={property.main_image || 'https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=150&h=150&fit=crop&crop=center'}
                        alt={property.name}
                        className="w-24 h-24 object-cover rounded-lg"
                      />

                      <div className="flex-1">
                        <div className="flex items-start justify-between">
                          <div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-1">
                              {property.name}
                            </h3>
                            <div className="flex items-center text-gray-600 mb-2">
                              <MapPin className="h-4 w-4 mr-1" />
                              <span>{property.city}</span>
                              <span className="mx-2">•</span>
                              <span className="capitalize">{property.property_type}</span>
                            </div>
                            <div className="flex items-center space-x-4 mb-2">
                              <div className="flex items-center text-yellow-500">
                                <Star className="h-4 w-4 fill-current mr-1" />
                                <span>{property.avg_rating.toFixed(1)} ({property.review_count} ulasan)</span>
                              </div>
                              <div className="flex items-center text-gray-600">
                                <Users className="h-4 w-4 mr-1" />
                                <span>{property.available_rooms} kamar tersedia</span>
                              </div>
                            </div>
                            {property.favorited_at && (
                              <p className="text-sm text-gray-500">
                                Ditambahkan {formatRelativeTime(property.favorited_at)}
                              </p>
                            )}
                          </div>

                          <div className="text-right">
                            <div className="text-xl font-bold text-primary mb-2">
                              {formatPrice(property.min_price)}
                              {property.min_price !== property.max_price && (
                                <span className="text-lg text-gray-500"> - {formatPrice(property.max_price)}</span>
                              )}
                            </div>
                            <div className="text-gray-500 text-sm mb-4">/bulan</div>

                            <div className="flex items-center space-x-2">
                              <Link
                                to={`/properties/${property.id}`}
                                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors text-sm"
                              >
                                Lihat Detail
                              </Link>
                              <button
                                onClick={() => handleShare(property)}
                                className="p-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                              >
                                <Share2 className="h-4 w-4 text-gray-600" />
                              </button>
                              <button
                                onClick={() => handleRemoveFavorite(property.id)}
                                className="p-2 border border-red-300 rounded-md hover:bg-red-50 transition-colors"
                              >
                                <Heart className="h-4 w-4 text-red-600 fill-current" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}

export default FavoritesPage
