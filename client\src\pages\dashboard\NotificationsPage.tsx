import React, { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { usersApi, type Notification } from '../../lib/api'
import { formatRelativeTime } from '../../lib/utils'
import {
  Bell,
  Heart,
  Eye,
  AlertCircle,
  Check,
  Check<PERSON>heck,
  Trash2,
  Filter,
  MoreVertical
} from 'lucide-react'
import { toast } from 'sonner'
import LoadingSpinner from '../../components/ui/LoadingSpinner'

const NotificationsPage: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<'all' | 'unread' | 'read'>('all')
  const [selectedItems, setSelectedItems] = useState<string[]>([])

  useEffect(() => {
    fetchNotifications()
  }, [])

  const fetchNotifications = async () => {
    try {
      setLoading(true)
      const response = await usersApi.getNotifications({ limit: 50 })
      setNotifications(response.data.data.notifications)
    } catch (error: any) {
      toast.error('Gagal memuat notifikasi')
      console.error('Error fetching notifications:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await usersApi.markNotificationRead(notificationId)
      setNotifications(prev =>
        prev.map(notif =>
          notif.id === notificationId
            ? { ...notif, is_read: true }
            : notif
        )
      )
      toast.success('Notifikasi ditandai sebagai dibaca')
    } catch (error: any) {
      toast.error('Gagal menandai notifikasi')
    }
  }

  const handleMarkAllAsRead = async () => {
    try {
      await usersApi.markAllNotificationsRead()
      setNotifications(prev =>
        prev.map(notif => ({ ...notif, is_read: true }))
      )
      toast.success('Semua notifikasi ditandai sebagai dibaca')
    } catch (error: any) {
      toast.error('Gagal menandai semua notifikasi')
    }
  }

  const handleSelectItem = (notificationId: string) => {
    setSelectedItems(prev =>
      prev.includes(notificationId)
        ? prev.filter(id => id !== notificationId)
        : [...prev, notificationId]
    )
  }

  const handleSelectAll = () => {
    if (selectedItems.length === filteredNotifications.length) {
      setSelectedItems([])
    } else {
      setSelectedItems(filteredNotifications.map(n => n.id))
    }
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'view_notification':
        return <Eye className="h-5 w-5 text-blue-500" />
      case 'favorite_added':
        return <Heart className="h-5 w-5 text-red-500" />
      case 'system':
        return <AlertCircle className="h-5 w-5 text-orange-500" />
      default:
        return <Bell className="h-5 w-5 text-gray-500" />
    }
  }

  const getNotificationColor = (type: string, isRead: boolean) => {
    if (isRead) return 'bg-gray-50 border-gray-200'

    switch (type) {
      case 'view_notification':
        return 'bg-blue-50 border-blue-200'
      case 'favorite_added':
        return 'bg-red-50 border-red-200'
      case 'system':
        return 'bg-orange-50 border-orange-200'
      default:
        return 'bg-gray-50 border-gray-200'
    }
  }

  // Filter notifications
  const filteredNotifications = notifications.filter(notification => {
    switch (filter) {
      case 'unread':
        return !notification.is_read
      case 'read':
        return notification.is_read
      default:
        return true
    }
  })

  const unreadCount = notifications.filter(n => !n.is_read).length

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="xl" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Notifikasi</h1>
            <p className="text-gray-600">
              {unreadCount > 0 ? `${unreadCount} notifikasi belum dibaca` : 'Semua notifikasi sudah dibaca'}
            </p>
          </div>

          {unreadCount > 0 && (
            <button
              onClick={handleMarkAllAsRead}
              className="flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
            >
              <CheckCheck className="h-4 w-4 mr-2" />
              Tandai Semua Dibaca
            </button>
          )}
        </div>

        {notifications.length === 0 ? (
          <div className="text-center py-16">
            <Bell className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-2">
              Tidak ada notifikasi
            </h3>
            <p className="text-gray-600 mb-6">
              Notifikasi akan muncul di sini ketika ada aktivitas terkait akun Anda
            </p>
            <Link to="/properties" className="btn-primary">
              Mulai Mencari Kost
            </Link>
          </div>
        ) : (
          <>
            {/* Controls */}
            <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                <div className="flex items-center space-x-4">
                  {/* Select All */}
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedItems.length === filteredNotifications.length && filteredNotifications.length > 0}
                      onChange={handleSelectAll}
                      className="rounded border-gray-300 text-primary focus:ring-primary"
                    />
                    <span className="ml-2 text-sm text-gray-700">Pilih Semua</span>
                  </label>

                  {/* Filter */}
                  <select
                    value={filter}
                    onChange={(e) => setFilter(e.target.value as any)}
                    className="text-sm border border-gray-300 rounded-md px-3 py-1"
                  >
                    <option value="all">Semua Notifikasi</option>
                    <option value="unread">Belum Dibaca ({unreadCount})</option>
                    <option value="read">Sudah Dibaca</option>
                  </select>
                </div>

                {selectedItems.length > 0 && (
                  <div className="flex items-center space-x-3">
                    <span className="text-sm text-gray-600">
                      {selectedItems.length} dipilih
                    </span>
                    <button className="flex items-center px-3 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors text-sm">
                      <Trash2 className="h-4 w-4 mr-2" />
                      Hapus
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* Notifications List */}
            <div className="space-y-3">
              {filteredNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`border rounded-lg p-4 transition-colors ${getNotificationColor(notification.type, notification.is_read)}`}
                >
                  <div className="flex items-start space-x-4">
                    <input
                      type="checkbox"
                      checked={selectedItems.includes(notification.id)}
                      onChange={() => handleSelectItem(notification.id)}
                      className="mt-1 rounded border-gray-300 text-primary focus:ring-primary"
                    />

                    <div className="flex-shrink-0 mt-1">
                      {getNotificationIcon(notification.type)}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className={`text-sm font-medium ${notification.is_read ? 'text-gray-700' : 'text-gray-900'}`}>
                            {notification.title}
                          </h3>
                          <p className={`text-sm mt-1 ${notification.is_read ? 'text-gray-500' : 'text-gray-700'}`}>
                            {notification.message}
                          </p>
                          {notification.property_name && (
                            <p className="text-xs text-gray-500 mt-1">
                              Terkait: {notification.property_name}
                            </p>
                          )}
                          <p className="text-xs text-gray-500 mt-2">
                            {formatRelativeTime(notification.created_at)}
                          </p>
                        </div>

                        <div className="flex items-center space-x-2 ml-4">
                          {!notification.is_read && (
                            <button
                              onClick={() => handleMarkAsRead(notification.id)}
                              className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                              title="Tandai sebagai dibaca"
                            >
                              <Check className="h-4 w-4" />
                            </button>
                          )}

                          <div className="relative">
                            <button className="p-1 text-gray-400 hover:text-gray-600 transition-colors">
                              <MoreVertical className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Load More */}
            {filteredNotifications.length >= 50 && (
              <div className="text-center mt-8">
                <button className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                  Muat Lebih Banyak
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}

export default NotificationsPage
