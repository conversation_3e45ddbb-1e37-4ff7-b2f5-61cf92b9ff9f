import React, { useState } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import { authApi } from '../../lib/api'
import { formatDate, isValidEmail, isValidPhoneNumber } from '../../lib/utils'
import {
  User,
  Mail,
  Phone,
  Calendar,
  Shield,
  Eye,
  EyeOff,
  Save,
  Edit,
  Check,
  X
} from 'lucide-react'
import { toast } from 'sonner'

const ProfilePage: React.FC = () => {
  const { user, updateUser } = useAuth()

  const [isEditingProfile, setIsEditingProfile] = useState(false)
  const [isChangingPassword, setIsChangingPassword] = useState(false)
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const [profileForm, setProfileForm] = useState({
    full_name: user?.full_name || '',
    phone: user?.phone || ''
  })

  const [passwordForm, setPasswordForm] = useState({
    current_password: '',
    new_password: '',
    confirm_password: ''
  })

  const [loading, setLoading] = useState(false)

  const handleProfileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setProfileForm({
      ...profileForm,
      [e.target.name]: e.target.value
    })
  }

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPasswordForm({
      ...passwordForm,
      [e.target.name]: e.target.value
    })
  }

  const handleProfileSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (profileForm.full_name.trim().length < 2) {
      toast.error('Nama lengkap minimal 2 karakter')
      return
    }

    if (profileForm.phone && !isValidPhoneNumber(profileForm.phone)) {
      toast.error('Format nomor telepon tidak valid')
      return
    }

    try {
      setLoading(true)
      const response = await authApi.updateProfile({
        full_name: profileForm.full_name.trim(),
        phone: profileForm.phone.trim() || undefined
      })

      updateUser(response.data.data.user)
      setIsEditingProfile(false)
      toast.success('Profil berhasil diperbarui')
    } catch (error: any) {
      toast.error(error.response?.data?.error?.message || 'Gagal memperbarui profil')
    } finally {
      setLoading(false)
    }
  }

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (passwordForm.new_password.length < 6) {
      toast.error('Password baru minimal 6 karakter')
      return
    }

    if (passwordForm.new_password !== passwordForm.confirm_password) {
      toast.error('Konfirmasi password tidak cocok')
      return
    }

    try {
      setLoading(true)
      await authApi.changePassword({
        current_password: passwordForm.current_password,
        new_password: passwordForm.new_password
      })

      setPasswordForm({
        current_password: '',
        new_password: '',
        confirm_password: ''
      })
      setIsChangingPassword(false)
      toast.success('Password berhasil diubah')
    } catch (error: any) {
      toast.error(error.response?.data?.error?.message || 'Gagal mengubah password')
    } finally {
      setLoading(false)
    }
  }

  const handleCancelEdit = () => {
    setProfileForm({
      full_name: user?.full_name || '',
      phone: user?.phone || ''
    })
    setIsEditingProfile(false)
  }

  const handleCancelPasswordChange = () => {
    setPasswordForm({
      current_password: '',
      new_password: '',
      confirm_password: ''
    })
    setIsChangingPassword(false)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Profil Saya</h1>
          <p className="text-gray-600">
            Kelola informasi profil dan keamanan akun Anda
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Profile Card */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="text-center">
                <div className="w-24 h-24 bg-primary text-white rounded-full flex items-center justify-center text-3xl font-bold mx-auto mb-4">
                  {user?.full_name?.charAt(0).toUpperCase()}
                </div>
                <h2 className="text-xl font-semibold text-gray-900 mb-1">
                  {user?.full_name}
                </h2>
                <p className="text-gray-600 mb-2">{user?.email}</p>
                <div className="flex items-center justify-center space-x-2 mb-4">
                  <Shield className="h-4 w-4 text-green-500" />
                  <span className="text-sm text-green-600 capitalize">
                    {user?.role}
                  </span>
                </div>
                <div className="text-sm text-gray-500">
                  Bergabung {user?.created_at && formatDate(user.created_at)}
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Profile Information */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Informasi Profil</h3>
                {!isEditingProfile && (
                  <button
                    onClick={() => setIsEditingProfile(true)}
                    className="flex items-center px-3 py-2 text-primary hover:bg-primary/10 rounded-md transition-colors"
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </button>
                )}
              </div>

              {isEditingProfile ? (
                <form onSubmit={handleProfileSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Nama Lengkap
                    </label>
                    <input
                      type="text"
                      name="full_name"
                      value={profileForm.full_name}
                      onChange={handleProfileChange}
                      className="input"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Nomor Telepon
                    </label>
                    <input
                      type="tel"
                      name="phone"
                      value={profileForm.phone}
                      onChange={handleProfileChange}
                      className="input"
                      placeholder="Contoh: 081234567890"
                    />
                  </div>

                  <div className="flex items-center space-x-3 pt-4">
                    <button
                      type="submit"
                      disabled={loading}
                      className="flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 disabled:opacity-50 transition-colors"
                    >
                      <Save className="h-4 w-4 mr-2" />
                      {loading ? 'Menyimpan...' : 'Simpan'}
                    </button>
                    <button
                      type="button"
                      onClick={handleCancelEdit}
                      className="flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                    >
                      <X className="h-4 w-4 mr-2" />
                      Batal
                    </button>
                  </div>
                </form>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <User className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Nama Lengkap</p>
                      <p className="font-medium text-gray-900">{user?.full_name}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <Mail className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Email</p>
                      <p className="font-medium text-gray-900">{user?.email}</p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <Phone className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Nomor Telepon</p>
                      <p className="font-medium text-gray-900">
                        {user?.phone || 'Belum diatur'}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <Calendar className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Tanggal Bergabung</p>
                      <p className="font-medium text-gray-900">
                        {user?.created_at && formatDate(user.created_at)}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Password Change */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Keamanan Akun</h3>
                {!isChangingPassword && (
                  <button
                    onClick={() => setIsChangingPassword(true)}
                    className="flex items-center px-3 py-2 text-primary hover:bg-primary/10 rounded-md transition-colors"
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Ubah Password
                  </button>
                )}
              </div>

              {isChangingPassword ? (
                <form onSubmit={handlePasswordSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Password Saat Ini
                    </label>
                    <div className="relative">
                      <input
                        type={showCurrentPassword ? 'text' : 'password'}
                        name="current_password"
                        value={passwordForm.current_password}
                        onChange={handlePasswordChange}
                        className="input pr-10"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                      >
                        {showCurrentPassword ? (
                          <EyeOff className="h-4 w-4 text-gray-400" />
                        ) : (
                          <Eye className="h-4 w-4 text-gray-400" />
                        )}
                      </button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Password Baru
                    </label>
                    <div className="relative">
                      <input
                        type={showNewPassword ? 'text' : 'password'}
                        name="new_password"
                        value={passwordForm.new_password}
                        onChange={handlePasswordChange}
                        className="input pr-10"
                        required
                        minLength={6}
                      />
                      <button
                        type="button"
                        onClick={() => setShowNewPassword(!showNewPassword)}
                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                      >
                        {showNewPassword ? (
                          <EyeOff className="h-4 w-4 text-gray-400" />
                        ) : (
                          <Eye className="h-4 w-4 text-gray-400" />
                        )}
                      </button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Konfirmasi Password Baru
                    </label>
                    <div className="relative">
                      <input
                        type={showConfirmPassword ? 'text' : 'password'}
                        name="confirm_password"
                        value={passwordForm.confirm_password}
                        onChange={handlePasswordChange}
                        className="input pr-10"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                      >
                        {showConfirmPassword ? (
                          <EyeOff className="h-4 w-4 text-gray-400" />
                        ) : (
                          <Eye className="h-4 w-4 text-gray-400" />
                        )}
                      </button>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 pt-4">
                    <button
                      type="submit"
                      disabled={loading}
                      className="flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 disabled:opacity-50 transition-colors"
                    >
                      <Save className="h-4 w-4 mr-2" />
                      {loading ? 'Mengubah...' : 'Ubah Password'}
                    </button>
                    <button
                      type="button"
                      onClick={handleCancelPasswordChange}
                      className="flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                    >
                      <X className="h-4 w-4 mr-2" />
                      Batal
                    </button>
                  </div>
                </form>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Shield className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Password</p>
                      <p className="font-medium text-gray-900">••••••••</p>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600">
                    Terakhir diubah: Tidak diketahui
                  </p>
                </div>
              )}
            </div>

            {/* Account Status */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Status Akun</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Status Akun</span>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    user?.is_active
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    <Check className="h-3 w-3 mr-1" />
                    {user?.is_active ? 'Aktif' : 'Tidak Aktif'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Email Terverifikasi</span>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    user?.email_verified
                      ? 'bg-green-100 text-green-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {user?.email_verified ? (
                      <>
                        <Check className="h-3 w-3 mr-1" />
                        Terverifikasi
                      </>
                    ) : (
                      <>
                        <X className="h-3 w-3 mr-1" />
                        Belum Terverifikasi
                      </>
                    )}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-700">Tipe Akun</span>
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 capitalize">
                    {user?.role}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ProfilePage
