import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import { propertiesApi, usersApi, type Property, type Notification } from '../../lib/api'
import { formatPrice, formatRelativeTime } from '../../lib/utils'
import { 
  Building2, 
  Plus, 
  Eye, 
  Heart, 
  Star, 
  TrendingUp,
  Users,
  Calendar,
  Bell,
  Settings,
  BarChart3,
  MapPin,
  Edit,
  Trash2
} from 'lucide-react'
import { toast } from 'sonner'
import LoadingSpinner from '../../components/ui/LoadingSpinner'

const OwnerDashboardPage: React.FC = () => {
  const { user } = useAuth()
  
  const [properties, setProperties] = useState<Property[]>([])
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    totalProperties: 0,
    totalViews: 0,
    totalFavorites: 0,
    availableRooms: 0,
    monthlyViews: 0,
    monthlyFavorites: 0
  })

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      
      // Fetch owner's properties
      const propertiesResponse = await propertiesApi.getMyProperties({ limit: 5 })
      const ownerProperties = propertiesResponse.data.data.properties || []
      setProperties(ownerProperties)
      
      // Calculate stats
      const totalProperties = ownerProperties.length
      const availableRooms = ownerProperties.reduce((sum, p) => sum + p.available_rooms, 0)
      
      setStats(prev => ({
        ...prev,
        totalProperties,
        availableRooms
      }))
      
      // Fetch notifications
      const notificationsResponse = await usersApi.getNotifications({ limit: 5 })
      setNotifications(notificationsResponse.data.data.notifications)
      
    } catch (error: any) {
      console.error('Error fetching dashboard data:', error)
      toast.error('Gagal memuat data dashboard')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="xl" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Dashboard Pemilik
            </h1>
            <p className="text-gray-600">
              Kelola properti kost Anda dengan mudah
            </p>
          </div>
          <Link
            to="/owner/properties/new"
            className="flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            Tambah Kost Baru
          </Link>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-3 bg-blue-100 rounded-lg">
                <Building2 className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Kost</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalProperties}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-3 bg-green-100 rounded-lg">
                <Users className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Kamar Tersedia</p>
                <p className="text-2xl font-bold text-gray-900">{stats.availableRooms}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-3 bg-purple-100 rounded-lg">
                <Eye className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Views</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalViews}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center">
              <div className="p-3 bg-red-100 rounded-lg">
                <Heart className="h-6 w-6 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Favorit</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalFavorites}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Quick Actions */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Aksi Cepat</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Link
                  to="/owner/properties/new"
                  className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="p-2 bg-primary/10 rounded-lg mr-4">
                    <Plus className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">Tambah Kost Baru</h3>
                    <p className="text-sm text-gray-600">Daftarkan properti baru</p>
                  </div>
                </Link>

                <Link
                  to="/owner/properties"
                  className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="p-2 bg-blue-100 rounded-lg mr-4">
                    <Building2 className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">Kelola Kost</h3>
                    <p className="text-sm text-gray-600">Edit dan atur properti</p>
                  </div>
                </Link>

                <Link
                  to="/owner/analytics"
                  className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="p-2 bg-green-100 rounded-lg mr-4">
                    <BarChart3 className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">Lihat Statistik</h3>
                    <p className="text-sm text-gray-600">Analisis performa kost</p>
                  </div>
                </Link>

                <Link
                  to="/profile"
                  className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="p-2 bg-gray-100 rounded-lg mr-4">
                    <Settings className="h-5 w-5 text-gray-600" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">Pengaturan</h3>
                    <p className="text-sm text-gray-600">Kelola profil dan akun</p>
                  </div>
                </Link>
              </div>
            </div>

            {/* Recent Properties */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900">Kost Terbaru</h2>
                <Link
                  to="/owner/properties"
                  className="text-primary hover:text-primary/80 text-sm font-medium"
                >
                  Lihat Semua
                </Link>
              </div>

              {properties.length > 0 ? (
                <div className="space-y-4">
                  {properties.map((property) => (
                    <div key={property.id} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                      <img
                        src={property.main_image || 'https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=100&h=100&fit=crop&crop=center'}
                        alt={property.name}
                        className="w-16 h-16 object-cover rounded-lg"
                      />
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900">{property.name}</h3>
                        <div className="flex items-center text-gray-600 text-sm">
                          <MapPin className="h-3 w-3 mr-1" />
                          <span>{property.city}</span>
                        </div>
                        <div className="flex items-center space-x-4 mt-1">
                          <span className="text-primary font-medium">
                            {formatPrice(property.min_price)}/bulan
                          </span>
                          <div className="flex items-center text-yellow-500">
                            <Star className="h-3 w-3 fill-current mr-1" />
                            <span className="text-xs">{property.avg_rating.toFixed(1)}</span>
                          </div>
                          <span className="text-xs text-gray-500">
                            {property.available_rooms} kamar tersedia
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Link
                          to={`/owner/properties/${property.id}/edit`}
                          className="p-2 text-gray-600 hover:text-primary transition-colors"
                        >
                          <Edit className="h-4 w-4" />
                        </Link>
                        <Link
                          to={`/properties/${property.id}`}
                          className="text-primary hover:text-primary/80 text-sm font-medium"
                        >
                          Lihat
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Building2 className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-600 mb-4">Belum ada kost terdaftar</p>
                  <Link to="/owner/properties/new" className="btn-primary">
                    Tambah Kost Pertama
                  </Link>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Profile Summary */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center space-x-4 mb-4">
                <div className="w-12 h-12 bg-primary text-white rounded-full flex items-center justify-center text-lg font-medium">
                  {user?.full_name?.charAt(0).toUpperCase()}
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">{user?.full_name}</h3>
                  <p className="text-sm text-gray-600">Pemilik Kost</p>
                </div>
              </div>
              <Link
                to="/profile"
                className="block w-full text-center py-2 px-4 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
              >
                Edit Profil
              </Link>
            </div>

            {/* Recent Notifications */}
            <div className="bg-white rounded-lg shadow-sm p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-medium text-gray-900">Notifikasi Terbaru</h3>
                <Bell className="h-4 w-4 text-gray-400" />
              </div>

              {notifications.length > 0 ? (
                <div className="space-y-3">
                  {notifications.slice(0, 3).map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-3 rounded-lg border ${
                        notification.is_read ? 'bg-gray-50' : 'bg-blue-50 border-blue-200'
                      }`}
                    >
                      <p className="text-sm font-medium text-gray-900">
                        {notification.title}
                      </p>
                      <p className="text-xs text-gray-600 mt-1">
                        {notification.message}
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        {formatRelativeTime(notification.created_at)}
                      </p>
                    </div>
                  ))}
                  <Link
                    to="/notifications"
                    className="block text-center text-primary hover:text-primary/80 text-sm font-medium pt-2"
                  >
                    Lihat Semua Notifikasi
                  </Link>
                </div>
              ) : (
                <div className="text-center py-4">
                  <Bell className="h-8 w-8 text-gray-300 mx-auto mb-2" />
                  <p className="text-sm text-gray-600">Tidak ada notifikasi</p>
                </div>
              )}
            </div>

            {/* Quick Tips */}
            <div className="bg-gradient-to-br from-primary/10 to-primary/5 rounded-lg p-6">
              <h3 className="font-medium text-gray-900 mb-3">💡 Tips Pemilik</h3>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Upload foto berkualitas tinggi</li>
                <li>• Tulis deskripsi yang detail dan menarik</li>
                <li>• Respon cepat pertanyaan calon penyewa</li>
                <li>• Update ketersediaan kamar secara berkala</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default OwnerDashboardPage
