import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { propertiesApi, type Property } from '../../lib/api'
import { formatPrice, formatDate } from '../../lib/utils'
import { 
  Building2, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  Star, 
  MapPin, 
  Users,
  MoreVertical,
  Search,
  Filter,
  Grid,
  List,
  TrendingUp
} from 'lucide-react'
import { toast } from 'sonner'
import LoadingSpinner from '../../components/ui/LoadingSpinner'

const PropertiesManagementPage: React.FC = () => {
  const [properties, setProperties] = useState<Property[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive'>('all')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [selectedProperties, setSelectedProperties] = useState<string[]>([])

  useEffect(() => {
    fetchProperties()
  }, [])

  const fetchProperties = async () => {
    try {
      setLoading(true)
      const response = await propertiesApi.getMyProperties()
      setProperties(response.data.data.properties || [])
    } catch (error: any) {
      toast.error('Gagal memuat daftar properti')
      console.error('Error fetching properties:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteProperty = async (propertyId: string) => {
    if (!confirm('Apakah Anda yakin ingin menghapus properti ini?')) {
      return
    }

    try {
      await propertiesApi.delete(propertyId)
      setProperties(prev => prev.filter(p => p.id !== propertyId))
      toast.success('Properti berhasil dihapus')
    } catch (error: any) {
      toast.error('Gagal menghapus properti')
    }
  }

  const handleSelectProperty = (propertyId: string) => {
    setSelectedProperties(prev => 
      prev.includes(propertyId) 
        ? prev.filter(id => id !== propertyId)
        : [...prev, propertyId]
    )
  }

  const handleSelectAll = () => {
    if (selectedProperties.length === filteredProperties.length) {
      setSelectedProperties([])
    } else {
      setSelectedProperties(filteredProperties.map(p => p.id))
    }
  }

  // Filter properties
  const filteredProperties = properties.filter(property => {
    const matchesSearch = property.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         property.city.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesFilter = filterStatus === 'all' || 
                         (filterStatus === 'active' && property.available_rooms > 0) ||
                         (filterStatus === 'inactive' && property.available_rooms === 0)
    
    return matchesSearch && matchesFilter
  })

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="xl" />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Kelola Properti</h1>
            <p className="text-gray-600">
              {properties.length} properti terdaftar
            </p>
          </div>
          <Link
            to="/owner/properties/new"
            className="flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            Tambah Properti
          </Link>
        </div>

        {properties.length === 0 ? (
          <div className="text-center py-16">
            <Building2 className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-2">
              Belum ada properti
            </h3>
            <p className="text-gray-600 mb-6">
              Mulai dengan menambahkan properti kost pertama Anda
            </p>
            <Link to="/owner/properties/new" className="btn-primary">
              Tambah Properti Pertama
            </Link>
          </div>
        ) : (
          <>
            {/* Controls */}
            <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                <div className="flex items-center space-x-4">
                  {/* Search */}
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <input
                      type="text"
                      placeholder="Cari properti..."
                      className="pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>

                  {/* Filter */}
                  <select
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value as any)}
                    className="text-sm border border-gray-300 rounded-md px-3 py-2"
                  >
                    <option value="all">Semua Status</option>
                    <option value="active">Tersedia</option>
                    <option value="inactive">Penuh</option>
                  </select>

                  {/* Select All */}
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedProperties.length === filteredProperties.length && filteredProperties.length > 0}
                      onChange={handleSelectAll}
                      className="rounded border-gray-300 text-primary focus:ring-primary"
                    />
                    <span className="ml-2 text-sm text-gray-700">Pilih Semua</span>
                  </label>
                </div>

                <div className="flex items-center space-x-2">
                  {/* View Mode Toggle */}
                  <div className="flex border border-gray-300 rounded-md">
                    <button
                      onClick={() => setViewMode('grid')}
                      className={`p-2 ${viewMode === 'grid' ? 'bg-primary text-white' : 'text-gray-600 hover:bg-gray-50'}`}
                    >
                      <Grid className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`p-2 ${viewMode === 'list' ? 'bg-primary text-white' : 'text-gray-600 hover:bg-gray-50'}`}
                    >
                      <List className="h-4 w-4" />
                    </button>
                  </div>

                  {/* Bulk Actions */}
                  {selectedProperties.length > 0 && (
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600">
                        {selectedProperties.length} dipilih
                      </span>
                      <button className="flex items-center px-3 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors text-sm">
                        <Trash2 className="h-4 w-4 mr-2" />
                        Hapus
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Properties Grid/List */}
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredProperties.map((property) => (
                  <div key={property.id} className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow">
                    {/* Selection Checkbox */}
                    <div className="relative">
                      <img
                        src={property.main_image || 'https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=400&h=300&fit=crop&crop=center'}
                        alt={property.name}
                        className="w-full h-48 object-cover rounded-t-lg"
                      />
                      <div className="absolute top-2 left-2">
                        <input
                          type="checkbox"
                          checked={selectedProperties.includes(property.id)}
                          onChange={() => handleSelectProperty(property.id)}
                          className="rounded border-gray-300 text-primary focus:ring-primary"
                        />
                      </div>
                      <div className="absolute bottom-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
                        {property.property_type === 'putra' ? 'Putra' : 
                         property.property_type === 'putri' ? 'Putri' : 'Campur'}
                      </div>
                      <div className={`absolute top-2 right-2 px-2 py-1 rounded text-xs font-medium ${
                        property.available_rooms > 0 
                          ? 'bg-green-500 text-white' 
                          : 'bg-red-500 text-white'
                      }`}>
                        {property.available_rooms > 0 ? 'Tersedia' : 'Penuh'}
                      </div>
                    </div>

                    <div className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <h3 className="text-lg font-semibold text-gray-900 line-clamp-1">
                          {property.name}
                        </h3>
                        <div className="flex items-center text-yellow-500">
                          <Star className="h-4 w-4 fill-current" />
                          <span className="text-sm text-gray-600 ml-1">
                            {property.avg_rating.toFixed(1)}
                          </span>
                        </div>
                      </div>

                      <div className="flex items-center text-gray-600 mb-2">
                        <MapPin className="h-4 w-4 mr-1" />
                        <span className="text-sm">{property.city}</span>
                      </div>

                      <div className="flex items-center text-gray-600 mb-3">
                        <Users className="h-4 w-4 mr-1" />
                        <span className="text-sm">
                          {property.available_rooms} dari {property.total_rooms} kamar tersedia
                        </span>
                      </div>

                      <div className="mb-4">
                        <span className="text-lg font-bold text-primary">
                          {formatPrice(property.min_price)}
                          {property.min_price !== property.max_price && (
                            <span className="text-gray-500"> - {formatPrice(property.max_price)}</span>
                          )}
                        </span>
                        <span className="text-gray-500 text-sm">/bulan</span>
                      </div>

                      <div className="flex gap-2">
                        <Link
                          to={`/properties/${property.id}`}
                          className="flex-1 bg-gray-100 text-gray-700 text-center py-2 px-4 rounded-md hover:bg-gray-200 transition-colors text-sm"
                        >
                          <Eye className="h-4 w-4 inline mr-1" />
                          Lihat
                        </Link>
                        <Link
                          to={`/owner/properties/${property.id}/edit`}
                          className="flex-1 bg-primary text-white text-center py-2 px-4 rounded-md hover:bg-primary/90 transition-colors text-sm"
                        >
                          <Edit className="h-4 w-4 inline mr-1" />
                          Edit
                        </Link>
                        <button
                          onClick={() => handleDeleteProperty(property.id)}
                          className="p-2 border border-red-300 rounded-md hover:bg-red-50 transition-colors"
                        >
                          <Trash2 className="h-4 w-4 text-red-600" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {filteredProperties.map((property) => (
                  <div key={property.id} className="bg-white rounded-lg shadow-sm border p-6">
                    <div className="flex items-start space-x-4">
                      <input
                        type="checkbox"
                        checked={selectedProperties.includes(property.id)}
                        onChange={() => handleSelectProperty(property.id)}
                        className="mt-1 rounded border-gray-300 text-primary focus:ring-primary"
                      />
                      
                      <img
                        src={property.main_image || 'https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=150&h=150&fit=crop&crop=center'}
                        alt={property.name}
                        className="w-24 h-24 object-cover rounded-lg"
                      />
                      
                      <div className="flex-1">
                        <div className="flex items-start justify-between">
                          <div>
                            <h3 className="text-xl font-semibold text-gray-900 mb-1">
                              {property.name}
                            </h3>
                            <div className="flex items-center text-gray-600 mb-2">
                              <MapPin className="h-4 w-4 mr-1" />
                              <span>{property.city}</span>
                              <span className="mx-2">•</span>
                              <span className="capitalize">{property.property_type}</span>
                            </div>
                            <div className="flex items-center space-x-4 mb-2">
                              <div className="flex items-center text-yellow-500">
                                <Star className="h-4 w-4 fill-current mr-1" />
                                <span>{property.avg_rating.toFixed(1)} ({property.review_count} ulasan)</span>
                              </div>
                              <div className="flex items-center text-gray-600">
                                <Users className="h-4 w-4 mr-1" />
                                <span>{property.available_rooms}/{property.total_rooms} kamar</span>
                              </div>
                            </div>
                            <p className="text-sm text-gray-500">
                              Dibuat {formatDate(property.created_at)}
                            </p>
                          </div>
                          
                          <div className="text-right">
                            <div className="text-xl font-bold text-primary mb-2">
                              {formatPrice(property.min_price)}
                              {property.min_price !== property.max_price && (
                                <span className="text-lg text-gray-500"> - {formatPrice(property.max_price)}</span>
                              )}
                            </div>
                            <div className="text-gray-500 text-sm mb-4">/bulan</div>
                            
                            <div className={`inline-flex px-2 py-1 rounded-full text-xs font-medium mb-4 ${
                              property.available_rooms > 0 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {property.available_rooms > 0 ? 'Tersedia' : 'Penuh'}
                            </div>
                            
                            <div className="flex items-center space-x-2">
                              <Link
                                to={`/owner/properties/${property.id}/analytics`}
                                className="p-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                                title="Lihat Statistik"
                              >
                                <TrendingUp className="h-4 w-4 text-gray-600" />
                              </Link>
                              <Link
                                to={`/properties/${property.id}`}
                                className="px-3 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors text-sm"
                              >
                                Lihat
                              </Link>
                              <Link
                                to={`/owner/properties/${property.id}/edit`}
                                className="px-3 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors text-sm"
                              >
                                Edit
                              </Link>
                              <button
                                onClick={() => handleDeleteProperty(property.id)}
                                className="p-2 border border-red-300 rounded-md hover:bg-red-50 transition-colors"
                              >
                                <Trash2 className="h-4 w-4 text-red-600" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}

export default PropertiesManagementPage
