import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom'
import { propertiesApi, type Property } from '../../lib/api'
import { formatPrice, formatDate } from '../../lib/utils'
import { 
  ArrowLeft,
  Eye, 
  Heart, 
  TrendingUp, 
  TrendingDown,
  Calendar,
  Users,
  Star,
  MapPin,
  BarChart3,
  PieChart,
  Activity
} from 'lucide-react'
import { toast } from 'sonner'
import LoadingSpinner from '../../components/ui/LoadingSpinner'

interface PropertyStats {
  total_views: number
  monthly_views: number
  total_favorites: number
  monthly_favorites: number
  view_history: Array<{ date: string; views: number }>
}

const PropertyAnalyticsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const [property, setProperty] = useState<Property | null>(null)
  const [stats, setStats] = useState<PropertyStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d')

  useEffect(() => {
    if (id) {
      fetchPropertyData()
    }
  }, [id])

  const fetchPropertyData = async () => {
    try {
      setLoading(true)
      
      // Fetch property details
      const propertyResponse = await propertiesApi.getById(id!)
      setProperty(propertyResponse.data.data.property)
      
      // Fetch property statistics
      const statsResponse = await propertiesApi.getStatistics(id!)
      setStats(statsResponse.data.data)
      
    } catch (error: any) {
      toast.error('Gagal memuat data analitik')
      console.error('Error fetching analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="xl" />
      </div>
    )
  }

  if (!property || !stats) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">404</h1>
        <p className="text-gray-600 mb-8">Data analitik tidak ditemukan.</p>
        <Link to="/owner/properties" className="btn-primary">
          Kembali ke Daftar Properti
        </Link>
      </div>
    )
  }

  const viewsGrowth = stats.monthly_views > 0 ? 
    ((stats.monthly_views - (stats.total_views - stats.monthly_views)) / (stats.total_views - stats.monthly_views)) * 100 : 0

  const favoritesGrowth = stats.monthly_favorites > 0 ? 
    ((stats.monthly_favorites - (stats.total_favorites - stats.monthly_favorites)) / (stats.total_favorites - stats.monthly_favorites)) * 100 : 0

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link
              to="/owner/properties"
              className="flex items-center text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Kembali
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{property.name}</h1>
              <div className="flex items-center text-gray-600 mt-1">
                <MapPin className="h-4 w-4 mr-1" />
                <span>{property.address}</span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value as any)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            >
              <option value="7d">7 Hari Terakhir</option>
              <option value="30d">30 Hari Terakhir</option>
              <option value="90d">90 Hari Terakhir</option>
            </select>
            <Link
              to={`/owner/properties/${property.id}/edit`}
              className="btn-primary"
            >
              Edit Properti
            </Link>
          </div>
        </div>

        {/* Property Summary */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="flex items-center space-x-4">
              <img
                src={property.main_image || 'https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=100&h=100&fit=crop&crop=center'}
                alt={property.name}
                className="w-16 h-16 object-cover rounded-lg"
              />
              <div>
                <p className="text-sm text-gray-600">Harga</p>
                <p className="text-lg font-bold text-primary">
                  {formatPrice(property.min_price)}
                  {property.min_price !== property.max_price && (
                    <span className="text-sm text-gray-500"> - {formatPrice(property.max_price)}</span>
                  )}
                </p>
              </div>
            </div>

            <div className="text-center">
              <p className="text-sm text-gray-600">Rating</p>
              <div className="flex items-center justify-center">
                <Star className="h-5 w-5 text-yellow-500 fill-current mr-1" />
                <span className="text-lg font-bold">{property.avg_rating.toFixed(1)}</span>
                <span className="text-sm text-gray-500 ml-1">({property.review_count})</span>
              </div>
            </div>

            <div className="text-center">
              <p className="text-sm text-gray-600">Kamar</p>
              <p className="text-lg font-bold">
                {property.available_rooms}/{property.total_rooms}
              </p>
              <p className="text-xs text-gray-500">Tersedia</p>
            </div>

            <div className="text-center">
              <p className="text-sm text-gray-600">Tipe</p>
              <p className="text-lg font-bold capitalize">{property.property_type}</p>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Views</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total_views}</p>
              </div>
              <div className="p-3 bg-blue-100 rounded-lg">
                <Eye className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              {viewsGrowth >= 0 ? (
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
              )}
              <span className={`text-sm ${viewsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {Math.abs(viewsGrowth).toFixed(1)}%
              </span>
              <span className="text-sm text-gray-500 ml-1">vs bulan lalu</span>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Views Bulan Ini</p>
                <p className="text-2xl font-bold text-gray-900">{stats.monthly_views}</p>
              </div>
              <div className="p-3 bg-purple-100 rounded-lg">
                <Activity className="h-6 w-6 text-purple-600" />
              </div>
            </div>
            <div className="mt-4">
              <span className="text-sm text-gray-500">
                {((stats.monthly_views / stats.total_views) * 100).toFixed(1)}% dari total
              </span>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Favorit</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total_favorites}</p>
              </div>
              <div className="p-3 bg-red-100 rounded-lg">
                <Heart className="h-6 w-6 text-red-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              {favoritesGrowth >= 0 ? (
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              ) : (
                <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
              )}
              <span className={`text-sm ${favoritesGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {Math.abs(favoritesGrowth).toFixed(1)}%
              </span>
              <span className="text-sm text-gray-500 ml-1">vs bulan lalu</span>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Favorit Bulan Ini</p>
                <p className="text-2xl font-bold text-gray-900">{stats.monthly_favorites}</p>
              </div>
              <div className="p-3 bg-green-100 rounded-lg">
                <Users className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div className="mt-4">
              <span className="text-sm text-gray-500">
                {stats.total_favorites > 0 ? ((stats.monthly_favorites / stats.total_favorites) * 100).toFixed(1) : 0}% dari total
              </span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Views Chart */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Grafik Views</h3>
              <BarChart3 className="h-5 w-5 text-gray-400" />
            </div>
            
            {/* Simple chart representation */}
            <div className="space-y-3">
              {stats.view_history.slice(-7).map((item, index) => (
                <div key={index} className="flex items-center">
                  <div className="w-20 text-sm text-gray-600">
                    {formatDate(item.date)}
                  </div>
                  <div className="flex-1 mx-4">
                    <div className="bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-primary h-2 rounded-full"
                        style={{ 
                          width: `${Math.max((item.views / Math.max(...stats.view_history.map(h => h.views))) * 100, 5)}%` 
                        }}
                      ></div>
                    </div>
                  </div>
                  <div className="w-12 text-sm text-gray-900 text-right">
                    {item.views}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Performance Insights */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Insights</h3>
              <PieChart className="h-5 w-5 text-gray-400" />
            </div>
            
            <div className="space-y-4">
              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">📈 Performa Views</h4>
                <p className="text-sm text-blue-800">
                  Properti Anda mendapat {stats.monthly_views} views bulan ini.
                  {viewsGrowth > 0 && ` Naik ${viewsGrowth.toFixed(1)}% dari bulan lalu!`}
                </p>
              </div>

              <div className="p-4 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-900 mb-2">❤️ Tingkat Favorit</h4>
                <p className="text-sm text-green-800">
                  {stats.total_favorites > 0 ? (
                    `${((stats.total_favorites / stats.total_views) * 100).toFixed(1)}% pengunjung menambahkan ke favorit.`
                  ) : (
                    'Belum ada yang menambahkan ke favorit.'
                  )}
                </p>
              </div>

              <div className="p-4 bg-yellow-50 rounded-lg">
                <h4 className="font-medium text-yellow-900 mb-2">⭐ Rating</h4>
                <p className="text-sm text-yellow-800">
                  Rating {property.avg_rating.toFixed(1)} dari {property.review_count} ulasan.
                  {property.avg_rating >= 4.0 ? ' Excellent!' : property.avg_rating >= 3.0 ? ' Good!' : ' Perlu perbaikan.'}
                </p>
              </div>

              <div className="p-4 bg-purple-50 rounded-lg">
                <h4 className="font-medium text-purple-900 mb-2">🏠 Ketersediaan</h4>
                <p className="text-sm text-purple-800">
                  {property.available_rooms} dari {property.total_rooms} kamar tersedia.
                  {property.available_rooms === 0 && ' Properti penuh!'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Recommendations */}
        <div className="bg-white rounded-lg shadow-sm p-6 mt-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">💡 Rekomendasi</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 border border-gray-200 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">Tingkatkan Foto</h4>
              <p className="text-sm text-gray-600">
                Upload foto berkualitas tinggi untuk menarik lebih banyak pengunjung.
              </p>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">Update Deskripsi</h4>
              <p className="text-sm text-gray-600">
                Pastikan deskripsi properti selalu up-to-date dan menarik.
              </p>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">Respon Cepat</h4>
              <p className="text-sm text-gray-600">
                Balas pertanyaan calon penyewa dengan cepat untuk meningkatkan konversi.
              </p>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2">Harga Kompetitif</h4>
              <p className="text-sm text-gray-600">
                Bandingkan harga dengan properti serupa di area yang sama.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PropertyAnalyticsPage
