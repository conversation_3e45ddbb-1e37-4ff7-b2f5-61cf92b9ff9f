import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { <PERSON>rowserRouter } from 'react-router-dom'
import Navbar from '../../components/layout/Navbar'
import { AuthContext } from '../../contexts/AuthContext'
import { SocketContext } from '../../contexts/SocketContext'
import { createMockUser } from '../setup'

// Mock components
vi.mock('../../components/ui/NotificationBadge', () => ({
  default: () => <div data-testid="notification-badge">Notifications</div>
}))

const mockNavigate = vi.fn()
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  }
})

// Helper to render Navbar with context
const renderNavbar = (authValue: any, socketValue: any = {}) => {
  const defaultSocketValue = {
    socket: null,
    isConnected: false,
    emitPropertyView: vi.fn(),
    emitPropertyFavorite: vi.fn(),
    emitUserRegistration: vi.fn(),
    emitPropertyCreation: vi.fn(),
    ...socketValue
  }

  return render(
    <BrowserRouter>
      <AuthContext.Provider value={authValue}>
        <SocketContext.Provider value={defaultSocketValue}>
          <Navbar />
        </SocketContext.Provider>
      </AuthContext.Provider>
    </BrowserRouter>
  )
}

describe('Navbar Component', () => {
  describe('Guest User (Not Authenticated)', () => {
    const guestAuthValue = {
      user: null,
      token: null,
      isAuthenticated: false,
      login: vi.fn(),
      logout: vi.fn(),
      register: vi.fn(),
      updateProfile: vi.fn(),
      changePassword: vi.fn(),
      loading: false
    }

    it('should render logo and navigation links', () => {
      renderNavbar(guestAuthValue)
      
      expect(screen.getByText('KOST2')).toBeInTheDocument()
      expect(screen.getByText('Beranda')).toBeInTheDocument()
      expect(screen.getByText('Cari Kost')).toBeInTheDocument()
    })

    it('should show login and register buttons for guests', () => {
      renderNavbar(guestAuthValue)
      
      expect(screen.getByText('Masuk')).toBeInTheDocument()
      expect(screen.getByText('Daftar')).toBeInTheDocument()
    })

    it('should not show authenticated user features', () => {
      renderNavbar(guestAuthValue)
      
      expect(screen.queryByTestId('notification-badge')).not.toBeInTheDocument()
      expect(screen.queryByText('Dashboard')).not.toBeInTheDocument()
      expect(screen.queryByText('Favorit')).not.toBeInTheDocument()
    })

    it('should handle mobile menu toggle', () => {
      renderNavbar(guestAuthValue)
      
      const mobileMenuButton = screen.getByLabelText('Toggle mobile menu')
      fireEvent.click(mobileMenuButton)
      
      // Mobile menu should be visible
      expect(screen.getByRole('navigation')).toBeInTheDocument()
    })
  })

  describe('Authenticated Regular User', () => {
    const userAuthValue = {
      user: createMockUser({ role: 'user' }),
      token: 'mock-token',
      isAuthenticated: true,
      login: vi.fn(),
      logout: vi.fn(),
      register: vi.fn(),
      updateProfile: vi.fn(),
      changePassword: vi.fn(),
      loading: false
    }

    it('should show authenticated user features', () => {
      renderNavbar(userAuthValue)
      
      expect(screen.getByTestId('notification-badge')).toBeInTheDocument()
      expect(screen.getByText('Favorit')).toBeInTheDocument()
    })

    it('should show user menu with profile options', () => {
      renderNavbar(userAuthValue)
      
      const userMenuButton = screen.getByText('T') // First letter of "Test User"
      fireEvent.click(userMenuButton)
      
      expect(screen.getByText('Dashboard')).toBeInTheDocument()
      expect(screen.getByText('Profil')).toBeInTheDocument()
      expect(screen.getByText('Keluar')).toBeInTheDocument()
    })

    it('should handle logout', async () => {
      renderNavbar(userAuthValue)
      
      const userMenuButton = screen.getByText('T')
      fireEvent.click(userMenuButton)
      
      const logoutButton = screen.getByText('Keluar')
      fireEvent.click(logoutButton)
      
      await waitFor(() => {
        expect(userAuthValue.logout).toHaveBeenCalled()
      })
    })

    it('should not show owner-specific features', () => {
      renderNavbar(userAuthValue)
      
      const userMenuButton = screen.getByText('T')
      fireEvent.click(userMenuButton)
      
      expect(screen.queryByText('Kelola Kost')).not.toBeInTheDocument()
    })

    it('should not show admin-specific features', () => {
      renderNavbar(userAuthValue)
      
      const userMenuButton = screen.getByText('T')
      fireEvent.click(userMenuButton)
      
      expect(screen.queryByText('Kelola Pengguna')).not.toBeInTheDocument()
      expect(screen.queryByText('Kelola Properti')).not.toBeInTheDocument()
    })
  })

  describe('Authenticated Owner User', () => {
    const ownerAuthValue = {
      user: createMockUser({ role: 'owner', full_name: 'Owner User' }),
      token: 'mock-token',
      isAuthenticated: true,
      login: vi.fn(),
      logout: vi.fn(),
      register: vi.fn(),
      updateProfile: vi.fn(),
      changePassword: vi.fn(),
      loading: false
    }

    it('should show owner-specific features', () => {
      renderNavbar(ownerAuthValue)
      
      const userMenuButton = screen.getByText('O') // First letter of "Owner User"
      fireEvent.click(userMenuButton)
      
      expect(screen.getByText('Kelola Kost')).toBeInTheDocument()
    })

    it('should redirect to owner dashboard', () => {
      renderNavbar(ownerAuthValue)
      
      const userMenuButton = screen.getByText('O')
      fireEvent.click(userMenuButton)
      
      const dashboardLink = screen.getByText('Dashboard')
      expect(dashboardLink.closest('a')).toHaveAttribute('href', '/owner/dashboard')
    })

    it('should not show admin-specific features', () => {
      renderNavbar(ownerAuthValue)
      
      const userMenuButton = screen.getByText('O')
      fireEvent.click(userMenuButton)
      
      expect(screen.queryByText('Kelola Pengguna')).not.toBeInTheDocument()
    })
  })

  describe('Authenticated Admin User', () => {
    const adminAuthValue = {
      user: createMockUser({ role: 'admin', full_name: 'Admin User' }),
      token: 'mock-token',
      isAuthenticated: true,
      login: vi.fn(),
      logout: vi.fn(),
      register: vi.fn(),
      updateProfile: vi.fn(),
      changePassword: vi.fn(),
      loading: false
    }

    it('should show admin-specific features', () => {
      renderNavbar(adminAuthValue)
      
      const userMenuButton = screen.getByText('A') // First letter of "Admin User"
      fireEvent.click(userMenuButton)
      
      expect(screen.getByText('Kelola Pengguna')).toBeInTheDocument()
      expect(screen.getByText('Kelola Properti')).toBeInTheDocument()
    })

    it('should redirect to admin dashboard', () => {
      renderNavbar(adminAuthValue)
      
      const userMenuButton = screen.getByText('A')
      fireEvent.click(userMenuButton)
      
      const dashboardLink = screen.getByText('Dashboard')
      expect(dashboardLink.closest('a')).toHaveAttribute('href', '/admin/dashboard')
    })
  })

  describe('Responsive Behavior', () => {
    const userAuthValue = {
      user: createMockUser(),
      token: 'mock-token',
      isAuthenticated: true,
      login: vi.fn(),
      logout: vi.fn(),
      register: vi.fn(),
      updateProfile: vi.fn(),
      changePassword: vi.fn(),
      loading: false
    }

    it('should show mobile menu when hamburger is clicked', () => {
      renderNavbar(userAuthValue)
      
      const mobileMenuButton = screen.getByLabelText('Toggle mobile menu')
      fireEvent.click(mobileMenuButton)
      
      // Check if mobile navigation is visible
      expect(screen.getByRole('navigation')).toBeInTheDocument()
    })

    it('should close mobile menu when navigation link is clicked', () => {
      renderNavbar(userAuthValue)
      
      const mobileMenuButton = screen.getByLabelText('Toggle mobile menu')
      fireEvent.click(mobileMenuButton)
      
      // Click on a navigation link
      const homeLink = screen.getAllByText('Beranda')[0] // Get first occurrence
      fireEvent.click(homeLink)
      
      // Mobile menu should close (implementation detail may vary)
    })
  })

  describe('Search Functionality', () => {
    const userAuthValue = {
      user: createMockUser(),
      token: 'mock-token',
      isAuthenticated: true,
      login: vi.fn(),
      logout: vi.fn(),
      register: vi.fn(),
      updateProfile: vi.fn(),
      changePassword: vi.fn(),
      loading: false
    }

    it('should handle search input', () => {
      renderNavbar(userAuthValue)
      
      const searchInput = screen.getByPlaceholderText('Cari kost...')
      fireEvent.change(searchInput, { target: { value: 'Jakarta' } })
      
      expect(searchInput).toHaveValue('Jakarta')
    })

    it('should handle search form submission', () => {
      renderNavbar(userAuthValue)
      
      const searchInput = screen.getByPlaceholderText('Cari kost...')
      fireEvent.change(searchInput, { target: { value: 'Jakarta' } })
      fireEvent.submit(searchInput.closest('form')!)
      
      // Should navigate to properties page with search query
      expect(mockNavigate).toHaveBeenCalledWith('/properties?search=Jakarta')
    })
  })
})
