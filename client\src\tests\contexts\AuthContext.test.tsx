import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor, act } from '@testing-library/react'
import { AuthProvider, useAuth } from '../../contexts/AuthContext'
import { createMockUser, createMockApiResponse, setupFetchMock, setupFetchError } from '../setup'

// Test component to access auth context
const TestComponent = () => {
  const auth = useAuth()
  
  return (
    <div>
      <div data-testid="loading">{auth.loading ? 'loading' : 'not-loading'}</div>
      <div data-testid="authenticated">{auth.isAuthenticated ? 'authenticated' : 'not-authenticated'}</div>
      <div data-testid="user">{auth.user ? auth.user.full_name : 'no-user'}</div>
      <button onClick={() => auth.login('<EMAIL>', 'password')}>Login</button>
      <button onClick={() => auth.register('<EMAIL>', 'password', 'Test User')}>Register</button>
      <button onClick={() => auth.logout()}>Logout</button>
      <button onClick={() => auth.updateProfile({ full_name: 'Updated Name' })}>Update Profile</button>
      <button onClick={() => auth.changePassword('oldpass', 'newpass')}>Change Password</button>
    </div>
  )
}

const renderWithAuthProvider = () => {
  return render(
    <AuthProvider>
      <TestComponent />
    </AuthProvider>
  )
}

describe('AuthContext', () => {
  beforeEach(() => {
    localStorage.clear()
    vi.clearAllMocks()
  })

  describe('Initial State', () => {
    it('should start with unauthenticated state', () => {
      renderWithAuthProvider()
      
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading')
      expect(screen.getByTestId('authenticated')).toHaveTextContent('not-authenticated')
      expect(screen.getByTestId('user')).toHaveTextContent('no-user')
    })

    it('should restore authentication from localStorage', async () => {
      const mockUser = createMockUser()
      const mockToken = 'stored-token'
      
      // Mock localStorage
      const mockLocalStorage = localStorage as any
      mockLocalStorage.getItem.mockImplementation((key: string) => {
        if (key === 'token') return mockToken
        if (key === 'user') return JSON.stringify(mockUser)
        return null
      })

      // Mock API call to verify token
      setupFetchMock(createMockApiResponse({ user: mockUser }))

      renderWithAuthProvider()

      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('authenticated')
        expect(screen.getByTestId('user')).toHaveTextContent(mockUser.full_name)
      })
    })

    it('should handle invalid stored token', async () => {
      const mockLocalStorage = localStorage as any
      mockLocalStorage.getItem.mockImplementation((key: string) => {
        if (key === 'token') return 'invalid-token'
        return null
      })

      // Mock API call to return error
      setupFetchMock(createMockApiResponse(null, false), 401)

      renderWithAuthProvider()

      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('not-authenticated')
        expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('token')
        expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('user')
      })
    })
  })

  describe('Login', () => {
    it('should login successfully with valid credentials', async () => {
      const mockUser = createMockUser()
      const mockToken = 'login-token'
      
      setupFetchMock(createMockApiResponse({
        user: mockUser,
        token: mockToken
      }))

      renderWithAuthProvider()

      act(() => {
        screen.getByText('Login').click()
      })

      expect(screen.getByTestId('loading')).toHaveTextContent('loading')

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('not-loading')
        expect(screen.getByTestId('authenticated')).toHaveTextContent('authenticated')
        expect(screen.getByTestId('user')).toHaveTextContent(mockUser.full_name)
      })

      // Check localStorage
      const mockLocalStorage = localStorage as any
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('token', mockToken)
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('user', JSON.stringify(mockUser))
    })

    it('should handle login failure', async () => {
      setupFetchMock(createMockApiResponse(null, false), 401)

      renderWithAuthProvider()

      act(() => {
        screen.getByText('Login').click()
      })

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('not-loading')
        expect(screen.getByTestId('authenticated')).toHaveTextContent('not-authenticated')
      })
    })

    it('should handle network error during login', async () => {
      setupFetchError('Network error')

      renderWithAuthProvider()

      act(() => {
        screen.getByText('Login').click()
      })

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('not-loading')
        expect(screen.getByTestId('authenticated')).toHaveTextContent('not-authenticated')
      })
    })
  })

  describe('Register', () => {
    it('should register successfully', async () => {
      const mockUser = createMockUser()
      const mockToken = 'register-token'
      
      setupFetchMock(createMockApiResponse({
        user: mockUser,
        token: mockToken
      }))

      renderWithAuthProvider()

      act(() => {
        screen.getByText('Register').click()
      })

      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('authenticated')
        expect(screen.getByTestId('user')).toHaveTextContent(mockUser.full_name)
      })
    })

    it('should handle registration failure', async () => {
      setupFetchMock(createMockApiResponse(null, false), 400)

      renderWithAuthProvider()

      act(() => {
        screen.getByText('Register').click()
      })

      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('not-authenticated')
      })
    })
  })

  describe('Logout', () => {
    it('should logout successfully', async () => {
      const mockUser = createMockUser()
      const mockToken = 'logout-token'
      
      // Setup initial authenticated state
      const mockLocalStorage = localStorage as any
      mockLocalStorage.getItem.mockImplementation((key: string) => {
        if (key === 'token') return mockToken
        if (key === 'user') return JSON.stringify(mockUser)
        return null
      })

      // Mock initial auth check
      setupFetchMock(createMockApiResponse({ user: mockUser }))

      renderWithAuthProvider()

      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('authenticated')
      })

      // Mock logout API call
      setupFetchMock(createMockApiResponse({ message: 'Logged out' }))

      act(() => {
        screen.getByText('Logout').click()
      })

      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('not-authenticated')
        expect(screen.getByTestId('user')).toHaveTextContent('no-user')
      })

      // Check localStorage cleared
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('token')
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('user')
    })
  })

  describe('Update Profile', () => {
    it('should update profile successfully', async () => {
      const mockUser = createMockUser()
      const updatedUser = { ...mockUser, full_name: 'Updated Name' }
      
      // Setup initial authenticated state
      const mockLocalStorage = localStorage as any
      mockLocalStorage.getItem.mockImplementation((key: string) => {
        if (key === 'token') return 'token'
        if (key === 'user') return JSON.stringify(mockUser)
        return null
      })

      // Mock initial auth check
      setupFetchMock(createMockApiResponse({ user: mockUser }))

      renderWithAuthProvider()

      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('authenticated')
      })

      // Mock update profile API call
      setupFetchMock(createMockApiResponse({ user: updatedUser }))

      act(() => {
        screen.getByText('Update Profile').click()
      })

      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent('Updated Name')
      })

      // Check localStorage updated
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('user', JSON.stringify(updatedUser))
    })

    it('should handle profile update failure', async () => {
      const mockUser = createMockUser()
      
      // Setup initial authenticated state
      const mockLocalStorage = localStorage as any
      mockLocalStorage.getItem.mockImplementation((key: string) => {
        if (key === 'token') return 'token'
        if (key === 'user') return JSON.stringify(mockUser)
        return null
      })

      setupFetchMock(createMockApiResponse({ user: mockUser }))

      renderWithAuthProvider()

      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('authenticated')
      })

      // Mock failed update
      setupFetchMock(createMockApiResponse(null, false), 400)

      act(() => {
        screen.getByText('Update Profile').click()
      })

      await waitFor(() => {
        // User should remain the same
        expect(screen.getByTestId('user')).toHaveTextContent(mockUser.full_name)
      })
    })
  })

  describe('Change Password', () => {
    it('should change password successfully', async () => {
      const mockUser = createMockUser()
      
      // Setup initial authenticated state
      const mockLocalStorage = localStorage as any
      mockLocalStorage.getItem.mockImplementation((key: string) => {
        if (key === 'token') return 'token'
        if (key === 'user') return JSON.stringify(mockUser)
        return null
      })

      setupFetchMock(createMockApiResponse({ user: mockUser }))

      renderWithAuthProvider()

      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('authenticated')
      })

      // Mock change password API call
      setupFetchMock(createMockApiResponse({ message: 'Password changed' }))

      act(() => {
        screen.getByText('Change Password').click()
      })

      await waitFor(() => {
        // Should remain authenticated
        expect(screen.getByTestId('authenticated')).toHaveTextContent('authenticated')
      })
    })

    it('should handle password change failure', async () => {
      const mockUser = createMockUser()
      
      // Setup initial authenticated state
      const mockLocalStorage = localStorage as any
      mockLocalStorage.getItem.mockImplementation((key: string) => {
        if (key === 'token') return 'token'
        if (key === 'user') return JSON.stringify(mockUser)
        return null
      })

      setupFetchMock(createMockApiResponse({ user: mockUser }))

      renderWithAuthProvider()

      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('authenticated')
      })

      // Mock failed password change
      setupFetchMock(createMockApiResponse(null, false), 400)

      act(() => {
        screen.getByText('Change Password').click()
      })

      await waitFor(() => {
        // Should remain authenticated
        expect(screen.getByTestId('authenticated')).toHaveTextContent('authenticated')
      })
    })
  })
})
