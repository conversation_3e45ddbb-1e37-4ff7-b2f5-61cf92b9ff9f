import { describe, it, expect, vi, beforeEach } from 'vitest'
import { authApi, propertiesApi, usersApi } from '../../lib/api'
import { createMockUser, createMockProperty, createMockApiResponse, setupFetchMock, setupFetchError } from '../setup'

describe('API Functions', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    localStorage.clear()
  })

  describe('authApi', () => {
    describe('login', () => {
      it('should make login request with correct data', async () => {
        const mockResponse = createMockApiResponse({
          user: createMockUser(),
          token: 'test-token'
        })
        
        setupFetchMock(mockResponse)

        const result = await authApi.login('<EMAIL>', 'password123')

        expect(fetch).toHaveBeenCalledWith(
          'http://localhost:5000/api/auth/login',
          expect.objectContaining({
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: '<EMAIL>',
              password: 'password123'
            })
          })
        )

        expect(result.data).toEqual(mockResponse)
      })

      it('should handle login failure', async () => {
        setupFetchMock(createMockApiResponse(null, false), 401)

        await expect(authApi.login('<EMAIL>', 'wrongpassword'))
          .rejects.toThrow()
      })

      it('should handle network error', async () => {
        setupFetchError('Network error')

        await expect(authApi.login('<EMAIL>', 'password123'))
          .rejects.toThrow('Network error')
      })
    })

    describe('register', () => {
      it('should make register request with correct data', async () => {
        const mockResponse = createMockApiResponse({
          user: createMockUser(),
          token: 'test-token'
        })
        
        setupFetchMock(mockResponse)

        const result = await authApi.register('<EMAIL>', 'password123', 'Test User')

        expect(fetch).toHaveBeenCalledWith(
          'http://localhost:5000/api/auth/register',
          expect.objectContaining({
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: '<EMAIL>',
              password: 'password123',
              full_name: 'Test User'
            })
          })
        )

        expect(result.data).toEqual(mockResponse)
      })
    })

    describe('logout', () => {
      it('should make logout request with authorization header', async () => {
        const mockResponse = createMockApiResponse({ message: 'Logged out' })
        setupFetchMock(mockResponse)

        // Mock localStorage to return a token
        const mockLocalStorage = localStorage as any
        mockLocalStorage.getItem.mockReturnValue('test-token')

        const result = await authApi.logout()

        expect(fetch).toHaveBeenCalledWith(
          'http://localhost:5000/api/auth/logout',
          expect.objectContaining({
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer test-token'
            }
          })
        )

        expect(result.data).toEqual(mockResponse)
      })
    })

    describe('getProfile', () => {
      it('should make profile request with authorization header', async () => {
        const mockResponse = createMockApiResponse({ user: createMockUser() })
        setupFetchMock(mockResponse)

        const mockLocalStorage = localStorage as any
        mockLocalStorage.getItem.mockReturnValue('test-token')

        const result = await authApi.getMe()

        expect(fetch).toHaveBeenCalledWith(
          'http://localhost:5000/api/auth/me',
          expect.objectContaining({
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer test-token'
            }
          })
        )

        expect(result.data).toEqual(mockResponse)
      })
    })
  })

  describe('propertiesApi', () => {
    describe('getAll', () => {
      it('should fetch properties without authentication', async () => {
        const mockResponse = createMockApiResponse({
          properties: [createMockProperty()],
          pagination: {
            current_page: 1,
            total_pages: 1,
            total_items: 1,
            items_per_page: 12,
            has_next: false,
            has_prev: false
          }
        })
        
        setupFetchMock(mockResponse)

        const result = await propertiesApi.getAll()

        expect(fetch).toHaveBeenCalledWith(
          'http://localhost:5000/api/properties',
          expect.objectContaining({
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            }
          })
        )

        expect(result.data).toEqual(mockResponse)
      })

      it('should fetch properties with query parameters', async () => {
        const mockResponse = createMockApiResponse({
          properties: [createMockProperty()],
          pagination: {
            current_page: 1,
            total_pages: 1,
            total_items: 1,
            items_per_page: 12,
            has_next: false,
            has_prev: false
          }
        })
        
        setupFetchMock(mockResponse)

        const params = {
          page: 1,
          limit: 10,
          city: 'Jakarta',
          property_type: 'putra',
          min_price: 1000000,
          max_price: 2000000,
          search: 'kost'
        }

        await propertiesApi.getAll(params)

        const expectedUrl = new URL('http://localhost:5000/api/properties')
        Object.entries(params).forEach(([key, value]) => {
          expectedUrl.searchParams.set(key, value.toString())
        })

        expect(fetch).toHaveBeenCalledWith(
          expectedUrl.toString(),
          expect.objectContaining({
            method: 'GET'
          })
        )
      })

      it('should fetch properties with authentication when token exists', async () => {
        const mockResponse = createMockApiResponse({
          properties: [createMockProperty({ is_favorited: true })],
          pagination: {
            current_page: 1,
            total_pages: 1,
            total_items: 1,
            items_per_page: 12,
            has_next: false,
            has_prev: false
          }
        })
        
        setupFetchMock(mockResponse)

        const mockLocalStorage = localStorage as any
        mockLocalStorage.getItem.mockReturnValue('test-token')

        await propertiesApi.getAll()

        expect(fetch).toHaveBeenCalledWith(
          'http://localhost:5000/api/properties',
          expect.objectContaining({
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer test-token'
            }
          })
        )
      })
    })

    describe('getById', () => {
      it('should fetch property by ID', async () => {
        const mockProperty = createMockProperty()
        const mockResponse = createMockApiResponse({ property: mockProperty })
        
        setupFetchMock(mockResponse)

        const result = await propertiesApi.getById('1')

        expect(fetch).toHaveBeenCalledWith(
          'http://localhost:5000/api/properties/1',
          expect.objectContaining({
            method: 'GET'
          })
        )

        expect(result.data).toEqual(mockResponse)
      })
    })
  })

  describe('usersApi', () => {
    beforeEach(() => {
      const mockLocalStorage = localStorage as any
      mockLocalStorage.getItem.mockReturnValue('test-token')
    })

    describe('getFavorites', () => {
      it('should fetch user favorites with authentication', async () => {
        const mockResponse = createMockApiResponse({
          favorites: [createMockProperty()],
          pagination: {
            current_page: 1,
            total_pages: 1,
            total_items: 1,
            items_per_page: 12,
            has_next: false,
            has_prev: false
          }
        })
        
        setupFetchMock(mockResponse)

        const result = await usersApi.getFavorites()

        expect(fetch).toHaveBeenCalledWith(
          'http://localhost:5000/api/users/favorites',
          expect.objectContaining({
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer test-token'
            }
          })
        )

        expect(result.data).toEqual(mockResponse)
      })
    })

    describe('addFavorite', () => {
      it('should add property to favorites', async () => {
        const mockResponse = createMockApiResponse({ message: 'Property added to favorites' })
        setupFetchMock(mockResponse)

        const result = await usersApi.addFavorite('property-1')

        expect(fetch).toHaveBeenCalledWith(
          'http://localhost:5000/api/users/favorites',
          expect.objectContaining({
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer test-token'
            },
            body: JSON.stringify({ property_id: 'property-1' })
          })
        )

        expect(result.data).toEqual(mockResponse)
      })
    })

    describe('removeFavorite', () => {
      it('should remove property from favorites', async () => {
        const mockResponse = createMockApiResponse({ message: 'Property removed from favorites' })
        setupFetchMock(mockResponse)

        const result = await usersApi.removeFavorite('property-1')

        expect(fetch).toHaveBeenCalledWith(
          'http://localhost:5000/api/users/favorites/property-1',
          expect.objectContaining({
            method: 'DELETE',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer test-token'
            }
          })
        )

        expect(result.data).toEqual(mockResponse)
      })
    })

    describe('getNotifications', () => {
      it('should fetch user notifications', async () => {
        const mockResponse = createMockApiResponse({
          notifications: [],
          unread_count: 0
        })
        setupFetchMock(mockResponse)

        const result = await usersApi.getNotifications({ limit: 10 })

        expect(fetch).toHaveBeenCalledWith(
          'http://localhost:5000/api/users/notifications?limit=10',
          expect.objectContaining({
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer test-token'
            }
          })
        )

        expect(result.data).toEqual(mockResponse)
      })
    })
  })

  describe('Error Handling', () => {
    it('should handle HTTP error responses', async () => {
      setupFetchMock(createMockApiResponse(null, false), 400)

      await expect(authApi.login('<EMAIL>', 'password'))
        .rejects.toThrow()
    })

    it('should handle network errors', async () => {
      setupFetchError('Network error')

      await expect(authApi.login('<EMAIL>', 'password'))
        .rejects.toThrow('Network error')
    })

    it('should handle JSON parsing errors', async () => {
      const mockFetch = fetch as any
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => {
          throw new Error('Invalid JSON')
        }
      })

      await expect(authApi.login('<EMAIL>', 'password'))
        .rejects.toThrow('Invalid JSON')
    })
  })
})
