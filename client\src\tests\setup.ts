import '@testing-library/jest-dom'
import { vi } from 'vitest'

// Mock environment variables
Object.defineProperty(window, 'process', {
  value: {
    env: {
      REACT_APP_SERVER_URL: 'http://localhost:5000'
    }
  }
})

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock
})

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock fetch
global.fetch = vi.fn()

// Mock Socket.IO
vi.mock('socket.io-client', () => ({
  io: vi.fn(() => ({
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn(),
    connect: vi.fn(),
    disconnect: vi.fn(),
    connected: false,
  }))
}))

// Mock react-router-dom
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => vi.fn(),
    useLocation: () => ({
      pathname: '/',
      search: '',
      hash: '',
      state: null,
    }),
    useParams: () => ({}),
  }
})

// Mock Sonner toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
    warning: vi.fn(),
  },
  Toaster: () => null,
}))

// Helper function to create mock user
export const createMockUser = (overrides = {}) => ({
  id: '1',
  email: '<EMAIL>',
  full_name: 'Test User',
  role: 'user',
  is_active: true,
  email_verified: true,
  created_at: '2024-01-01T00:00:00Z',
  ...overrides
})

// Helper function to create mock property
export const createMockProperty = (overrides = {}) => ({
  id: '1',
  name: 'Test Kost Property',
  description: 'A test property for testing',
  address: 'Test Address 123',
  city: 'Test City',
  province: 'Test Province',
  property_type: 'campur' as const,
  total_rooms: 10,
  available_rooms: 5,
  min_price: 1000000,
  max_price: 1500000,
  avg_rating: 4.5,
  review_count: 10,
  main_image: 'https://example.com/image.jpg',
  is_favorited: false,
  created_at: '2024-01-01T00:00:00Z',
  ...overrides
})

// Helper function to create mock API response
export const createMockApiResponse = (data: any, success = true) => ({
  success,
  data,
  ...(success ? {} : { error: { message: 'Test error' } })
})

// Helper function to setup fetch mock
export const setupFetchMock = (response: any, status = 200) => {
  const mockFetch = fetch as any
  mockFetch.mockResolvedValueOnce({
    ok: status >= 200 && status < 300,
    status,
    json: async () => response,
  })
}

// Helper function to setup fetch mock with error
export const setupFetchError = (error: string) => {
  const mockFetch = fetch as any
  mockFetch.mockRejectedValueOnce(new Error(error))
}

// Clear all mocks before each test
beforeEach(() => {
  vi.clearAllMocks()
  localStorageMock.getItem.mockClear()
  localStorageMock.setItem.mockClear()
  localStorageMock.removeItem.mockClear()
  localStorageMock.clear.mockClear()
})
