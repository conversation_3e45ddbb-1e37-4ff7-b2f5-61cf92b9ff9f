-- KOST2 Database Seed Data
-- Insert initial data for development and testing
-- WARNING: This seed data is for development only. Do not use in production.
-- All passwords are hashed versions of 'DevPassword123!' for testing purposes.

-- Insert default facilities
INSERT INTO facilities (name, icon, category) VALUES
-- Room facilities
('AC', 'snowflake', 'room'),
('WiFi', 'wifi', 'room'),
('<PERSON><PERSON>', 'bath', 'room'),
('<PERSON>mari', 'cabinet', 'room'),
('Meja <PERSON>', 'desk', 'room'),
('Kasur', 'bed', 'room'),
('Jendela', 'window', 'room'),
('Balkon', 'balcony', 'room'),

-- Common facilities
('<PERSON><PERSON> Be<PERSON>ma', 'chef-hat', 'common'),
('Ruang Tamu', 'sofa', 'common'),
('Laundry', 'washing-machine', 'common'),
('Jemuran', 'sun', 'common'),
('Dispenser', 'droplets', 'common'),
('<PERSON><PERSON><PERSON>', 'refrigerator', 'common'),
('<PERSON><PERSON>', 'utensils', 'common'),
('Taman', 'trees', 'common'),

-- Security facilities
('CCTV', 'camera', 'security'),
('Satpam', 'shield-check', 'security'),
('Akses Card', 'credit-card', 'security'),
('Pagar', 'fence', 'security'),
('Kunci Ganda', 'key', 'security'),

-- Parking facilities
('Parkir Motor', 'bike', 'parking'),
('Parkir Mobil', 'car', 'parking'),
('Parkir Tertutup', 'garage', 'parking');

-- Insert admin user (password: DevPassword123!)
INSERT INTO users (email, password_hash, full_name, role, is_active, email_verified) VALUES
('<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VjWZifZTy', 'System Administrator', 'admin', true, true);

-- Insert sample owner users (password: DevPassword123!)
INSERT INTO users (email, password_hash, full_name, phone, role, is_active, email_verified) VALUES
('<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VjWZifZTy', 'Budi Santoso', '081234567890', 'owner', true, true),
('<EMAIL>', '$2b$12$8Xo3KzKzKzKzKzKzKzKzKOYz6TtxMQJqhN8/LewdBPj/VjWZifZTy', 'Siti Rahayu', '081234567891', 'owner', true, true),
('<EMAIL>', '$2b$12$9Yo4LzLzLzLzLzLzLzLzLOYz6TtxMQJqhN8/LewdBPj/VjWZifZTy', 'Ahmad Wijaya', '081234567892', 'owner', true, true);

-- Insert sample regular users (password: DevPassword123!)
INSERT INTO users (email, password_hash, full_name, phone, role, is_active, email_verified) VALUES
('<EMAIL>', '$2b$12$5Mn2MzMzMzMzMzMzMzMzMOYz6TtxMQJqhN8/LewdBPj/VjWZifZTy', 'Andi Pratama', '081234567893', 'user', true, true),
('<EMAIL>', '$2b$12$6Nn3NzNzNzNzNzNzNzNzNOYz6TtxMQJqhN8/LewdBPj/VjWZifZTy', 'Maya Sari', '081234567894', 'user', true, true),
('<EMAIL>', '$2b$12$7Oo4OzOzOzOzOzOzOzOzOOYz6TtxMQJqhN8/LewdBPj/VjWZifZTy', 'Rudi Hermawan', '081234567895', 'user', true, true);

-- Insert sample kost properties
INSERT INTO kost_properties (owner_id, name, description, address, city, province, postal_code, latitude, longitude, property_type, total_rooms, available_rooms) VALUES
((SELECT id FROM users WHERE email = '<EMAIL>'), 
 'Kost Melati Putri', 
 'Kost nyaman khusus putri dengan fasilitas lengkap di pusat kota. Dekat dengan kampus dan pusat perbelanjaan.',
 'Jl. Melati No. 15, Kelurahan Sumber, Kecamatan Banjarsari',
 'Surakarta',
 'Jawa Tengah',
 '57138',
 -7.5665,
 110.8167,
 'putri',
 20,
 15),

((SELECT id FROM users WHERE email = '<EMAIL>'),
 'Kost Mawar Putra',
 'Kost eksklusif untuk putra dengan fasilitas modern dan keamanan 24 jam. Lokasi strategis dekat stasiun.',
 'Jl. Mawar Raya No. 88, Kelurahan Manahan, Kecamatan Banjarsari',
 'Surakarta',
 'Jawa Tengah',
 '57139',
 -7.5514,
 110.8071,
 'putra',
 25,
 18),

((SELECT id FROM users WHERE email = '<EMAIL>'),
 'Kost Anggrek Campur',
 'Kost modern dengan konsep co-living untuk putra dan putri. Dilengkapi dengan co-working space dan gym.',
 'Jl. Anggrek Indah No. 22, Kelurahan Jajar, Kecamatan Laweyan',
 'Surakarta',
 'Jawa Tengah',
 '57144',
 -7.5804,
 110.7847,
 'campur',
 30,
 22);

-- Insert sample rooms for each property
-- Rooms for Kost Melati Putri
INSERT INTO rooms (property_id, room_number, room_type, price_monthly, size_sqm, has_ac, has_wifi, has_private_bathroom, has_wardrobe, has_desk, status) VALUES
((SELECT id FROM kost_properties WHERE name = 'Kost Melati Putri'), '101', 'Standard', 800000, 12.5, true, true, false, true, true, 'available'),
((SELECT id FROM kost_properties WHERE name = 'Kost Melati Putri'), '102', 'Standard', 800000, 12.5, true, true, false, true, true, 'occupied'),
((SELECT id FROM kost_properties WHERE name = 'Kost Melati Putri'), '103', 'Deluxe', 1200000, 16.0, true, true, true, true, true, 'available'),
((SELECT id FROM kost_properties WHERE name = 'Kost Melati Putri'), '201', 'Standard', 750000, 12.0, false, true, false, true, true, 'available'),
((SELECT id FROM kost_properties WHERE name = 'Kost Melati Putri'), '202', 'Deluxe', 1200000, 16.0, true, true, true, true, true, 'available');

-- Rooms for Kost Mawar Putra
INSERT INTO rooms (property_id, room_number, room_type, price_monthly, size_sqm, has_ac, has_wifi, has_private_bathroom, has_wardrobe, has_desk, status) VALUES
((SELECT id FROM kost_properties WHERE name = 'Kost Mawar Putra'), '101', 'Premium', 1500000, 20.0, true, true, true, true, true, 'available'),
((SELECT id FROM kost_properties WHERE name = 'Kost Mawar Putra'), '102', 'Standard', 900000, 14.0, true, true, false, true, true, 'available'),
((SELECT id FROM kost_properties WHERE name = 'Kost Mawar Putra'), '103', 'Standard', 900000, 14.0, true, true, false, true, true, 'occupied'),
((SELECT id FROM kost_properties WHERE name = 'Kost Mawar Putra'), '201', 'Premium', 1500000, 20.0, true, true, true, true, true, 'available'),
((SELECT id FROM kost_properties WHERE name = 'Kost Mawar Putra'), '202', 'Deluxe', 1200000, 16.0, true, true, true, true, true, 'maintenance');

-- Rooms for Kost Anggrek Campur
INSERT INTO rooms (property_id, room_number, room_type, price_monthly, size_sqm, has_ac, has_wifi, has_private_bathroom, has_wardrobe, has_desk, status) VALUES
((SELECT id FROM kost_properties WHERE name = 'Kost Anggrek Campur'), '101', 'Studio', 2000000, 25.0, true, true, true, true, true, 'available'),
((SELECT id FROM kost_properties WHERE name = 'Kost Anggrek Campur'), '102', 'Standard', 1000000, 15.0, true, true, false, true, true, 'available'),
((SELECT id FROM kost_properties WHERE name = 'Kost Anggrek Campur'), '103', 'Deluxe', 1400000, 18.0, true, true, true, true, true, 'occupied'),
((SELECT id FROM kost_properties WHERE name = 'Kost Anggrek Campur'), '201', 'Studio', 2000000, 25.0, true, true, true, true, true, 'available'),
((SELECT id FROM kost_properties WHERE name = 'Kost Anggrek Campur'), '202', 'Premium', 1600000, 20.0, true, true, true, true, true, 'available');

-- Insert property facilities relationships
-- Kost Melati Putri facilities
INSERT INTO property_facilities (property_id, facility_id, is_available) VALUES
((SELECT id FROM kost_properties WHERE name = 'Kost Melati Putri'), (SELECT id FROM facilities WHERE name = 'WiFi'), true),
((SELECT id FROM kost_properties WHERE name = 'Kost Melati Putri'), (SELECT id FROM facilities WHERE name = 'AC'), true),
((SELECT id FROM kost_properties WHERE name = 'Kost Melati Putri'), (SELECT id FROM facilities WHERE name = 'Dapur Bersama'), true),
((SELECT id FROM kost_properties WHERE name = 'Kost Melati Putri'), (SELECT id FROM facilities WHERE name = 'Laundry'), true),
((SELECT id FROM kost_properties WHERE name = 'Kost Melati Putri'), (SELECT id FROM facilities WHERE name = 'CCTV'), true),
((SELECT id FROM kost_properties WHERE name = 'Kost Melati Putri'), (SELECT id FROM facilities WHERE name = 'Parkir Motor'), true);

-- Kost Mawar Putra facilities
INSERT INTO property_facilities (property_id, facility_id, is_available) VALUES
((SELECT id FROM kost_properties WHERE name = 'Kost Mawar Putra'), (SELECT id FROM facilities WHERE name = 'WiFi'), true),
((SELECT id FROM kost_properties WHERE name = 'Kost Mawar Putra'), (SELECT id FROM facilities WHERE name = 'AC'), true),
((SELECT id FROM kost_properties WHERE name = 'Kost Mawar Putra'), (SELECT id FROM facilities WHERE name = 'Kamar Mandi Dalam'), true),
((SELECT id FROM kost_properties WHERE name = 'Kost Mawar Putra'), (SELECT id FROM facilities WHERE name = 'Satpam'), true),
((SELECT id FROM kost_properties WHERE name = 'Kost Mawar Putra'), (SELECT id FROM facilities WHERE name = 'Parkir Motor'), true),
((SELECT id FROM kost_properties WHERE name = 'Kost Mawar Putra'), (SELECT id FROM facilities WHERE name = 'Parkir Mobil'), true);

-- Kost Anggrek Campur facilities
INSERT INTO property_facilities (property_id, facility_id, is_available) VALUES
((SELECT id FROM kost_properties WHERE name = 'Kost Anggrek Campur'), (SELECT id FROM facilities WHERE name = 'WiFi'), true),
((SELECT id FROM kost_properties WHERE name = 'Kost Anggrek Campur'), (SELECT id FROM facilities WHERE name = 'AC'), true),
((SELECT id FROM kost_properties WHERE name = 'Kost Anggrek Campur'), (SELECT id FROM facilities WHERE name = 'Kamar Mandi Dalam'), true),
((SELECT id FROM kost_properties WHERE name = 'Kost Anggrek Campur'), (SELECT id FROM facilities WHERE name = 'Ruang Tamu'), true),
((SELECT id FROM kost_properties WHERE name = 'Kost Anggrek Campur'), (SELECT id FROM facilities WHERE name = 'Taman'), true),
((SELECT id FROM kost_properties WHERE name = 'Kost Anggrek Campur'), (SELECT id FROM facilities WHERE name = 'CCTV'), true),
((SELECT id FROM kost_properties WHERE name = 'Kost Anggrek Campur'), (SELECT id FROM facilities WHERE name = 'Parkir Tertutup'), true);

-- Insert sample testimonials
INSERT INTO testimonials (property_id, user_id, rating, comment, is_approved) VALUES
((SELECT id FROM kost_properties WHERE name = 'Kost Melati Putri'), 
 (SELECT id FROM users WHERE email = '<EMAIL>'), 
 5, 
 'Kost yang sangat nyaman dan bersih. Pemilik kost juga ramah dan responsif. Highly recommended!', 
 true),

((SELECT id FROM kost_properties WHERE name = 'Kost Mawar Putra'), 
 (SELECT id FROM users WHERE email = '<EMAIL>'), 
 4, 
 'Fasilitas lengkap dan lokasi strategis. Hanya saja kadang air panas tidak stabil.', 
 true),

((SELECT id FROM kost_properties WHERE name = 'Kost Anggrek Campur'), 
 (SELECT id FROM users WHERE email = '<EMAIL>'), 
 5, 
 'Konsep co-living yang menarik. Banyak teman baru dan suasana yang mendukung produktivitas.', 
 true);

-- Insert sample favorites
INSERT INTO favorites (user_id, property_id) VALUES
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM kost_properties WHERE name = 'Kost Mawar Putra')),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM kost_properties WHERE name = 'Kost Anggrek Campur')),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM kost_properties WHERE name = 'Kost Melati Putri')),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM kost_properties WHERE name = 'Kost Melati Putri'));

-- Insert sample property views
INSERT INTO property_views (property_id, user_id, ip_address, user_agent, viewed_at) VALUES
((SELECT id FROM kost_properties WHERE name = 'Kost Melati Putri'), (SELECT id FROM users WHERE email = '<EMAIL>'), '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL '2 days'),
((SELECT id FROM kost_properties WHERE name = 'Kost Melati Putri'), (SELECT id FROM users WHERE email = '<EMAIL>'), '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', NOW() - INTERVAL '1 day'),
((SELECT id FROM kost_properties WHERE name = 'Kost Mawar Putra'), (SELECT id FROM users WHERE email = '<EMAIL>'), '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL '3 hours'),
((SELECT id FROM kost_properties WHERE name = 'Kost Anggrek Campur'), NULL, '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15', NOW() - INTERVAL '1 hour');
