# Database Setup Instructions

## Prerequisites
- PostgreSQL 13+ installed on your system
- Access to PostgreSQL command line tools (psql)

## Setup Steps

### 1. Create Database and User

Open PostgreSQL command line (psql) as superuser and run:

```sql
-- Create database
CREATE DATABASE kost;

-- Create user (if doesn't exist)
CREATE USER postgres WITH PASSWORD 'vicky';

-- <PERSON> privileges
GRANT ALL PRIVILEGES ON DATABASE kost TO postgres;

-- Connect to the database
\c kost;

-- Grant schema privileges
GRANT ALL ON SCHEMA public TO postgres;
```

### 2. Run Schema Creation

From the project root directory, run:

```bash
# Navigate to database directory
cd database

# Run schema creation
psql -U postgres -d kost -f schema.sql

# Run seed data insertion
psql -U postgres -d kost -f seed.sql
```

### 3. Verify Setup

Connect to the database and verify tables:

```sql
-- Connect to database
psql -U postgres -d kost

-- List all tables
\dt

-- Check sample data
SELECT COUNT(*) FROM users;
SELECT COUNT(*) FROM kost_properties;
SELECT COUNT(*) FROM facilities;
```

## Alternative Setup (if user already exists)

If you already have a PostgreSQL user, update the `.env` file in the server directory:

```env
DB_USER=your_existing_user
DB_PASSWORD=your_password
```

## Troubleshooting

### Error: "role does not exist"
- Make sure PostgreSQL is running
- Create the user as shown in step 1
- Or update the `.env` file with existing credentials

### Error: "database does not exist"
- Create the database as shown in step 1
- Make sure you're connected to the correct PostgreSQL instance

### Error: "permission denied"
- Grant proper privileges as shown in step 1
- Make sure the user has access to the database

## Test Connection

After setup, you can test the connection by running:

```bash
cd server
npm run dev
```

You should see:
```
✅ Database connected successfully
📅 Database time: [current timestamp]
🚀 KOST2 Server running on port 5000
```
