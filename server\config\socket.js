const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const { query } = require('./database');

let io;

// Rate limiting for socket events
const rateLimitMap = new Map();
const RATE_LIMIT_WINDOW = 60000; // 1 minute
const MAX_EVENTS_PER_WINDOW = 10;

const checkRateLimit = (userId, eventType) => {
  const key = `${userId}:${eventType}`;
  const now = Date.now();

  if (!rateLimitMap.has(key)) {
    rateLimitMap.set(key, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return true;
  }

  const limit = rateLimitMap.get(key);

  if (now > limit.resetTime) {
    // Reset the limit
    rateLimitMap.set(key, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return true;
  }

  if (limit.count >= MAX_EVENTS_PER_WINDOW) {
    return false;
  }

  limit.count++;
  return true;
};

const initializeSocket = (server) => {
  io = new Server(server, {
    cors: {
      origin: process.env.CORS_ORIGIN || 'http://localhost:5173',
      methods: ['GET', 'POST'],
      credentials: true
    }
  });

  // Socket authentication middleware
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;
      
      if (!token) {
        return next(new Error('Authentication error'));
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      // Get user from database
      const userResult = await query(
        'SELECT id, email, full_name, role FROM users WHERE id = $1 AND is_active = true',
        [decoded.userId]
      );

      if (userResult.rows.length === 0) {
        return next(new Error('User not found'));
      }

      socket.user = userResult.rows[0];
      next();
    } catch (error) {
      next(new Error('Authentication error'));
    }
  });

  io.on('connection', (socket) => {
    console.log(`User ${socket.user.full_name} connected (${socket.user.role})`);

    // Join user to their role-specific room
    socket.join(`user_${socket.user.id}`);
    socket.join(`role_${socket.user.role}`);

    // If user is an owner, join them to owner-specific room
    if (socket.user.role === 'owner') {
      socket.join('owners');
    }

    // If user is an admin, join them to admin-specific room
    if (socket.user.role === 'admin') {
      socket.join('admins');
    }

    // Handle property view events
    socket.on('property_viewed', async (data) => {
      try {
        const { propertyId, viewerId } = data;

        // Validate input data
        if (!propertyId || !viewerId) {
          console.error('Invalid property view data:', data);
          return;
        }

        // Check rate limit
        if (!checkRateLimit(socket.user.id, 'property_viewed')) {
          console.warn(`Rate limit exceeded for user ${socket.user.id} on property_viewed`);
          return;
        }

        // Record the view in database
        await query(
          'INSERT INTO property_views (property_id, viewer_id) VALUES ($1, $2)',
          [propertyId, viewerId]
        );

        // Get property owner
        const propertyResult = await query(
          'SELECT owner_id, name FROM kost_properties WHERE id = $1',
          [propertyId]
        );

        if (propertyResult.rows.length > 0) {
          const { owner_id, name: propertyName } = propertyResult.rows[0];

          // Get viewer info
          const viewerResult = await query(
            'SELECT full_name FROM users WHERE id = $1',
            [viewerId]
          );

          if (viewerResult.rows.length > 0) {
            const viewerName = viewerResult.rows[0].full_name;

            // Create notification for property owner
            const notificationResult = await query(
              `INSERT INTO notifications (user_id, type, title, message, data)
               VALUES ($1, 'property_view', $2, $3, $4)
               RETURNING *`,
              [
                owner_id,
                'Properti Dilihat',
                `${viewerName} melihat properti ${propertyName}`,
                JSON.stringify({ property_id: propertyId, viewer_id: viewerId })
              ]
            );

            // Send real-time notification to property owner
            io.to(`user_${owner_id}`).emit('new_notification', {
              notification: notificationResult.rows[0],
              type: 'property_view'
            });

            // Send analytics update to owner
            const viewCountResult = await query(
              'SELECT COUNT(*) as total_views FROM property_views WHERE property_id = $1',
              [propertyId]
            );

            io.to(`user_${owner_id}`).emit('property_analytics_update', {
              property_id: propertyId,
              total_views: parseInt(viewCountResult.rows[0].total_views),
              latest_viewer: viewerName
            });
          }
        }
      } catch (error) {
        console.error('Error handling property view:', error);
      }
    });

    // Handle property favorite events
    socket.on('property_favorited', async (data) => {
      try {
        const { propertyId, userId, action } = data; // action: 'add' or 'remove'

        // Validate input data
        if (!propertyId || !userId || !action || !['add', 'remove'].includes(action)) {
          console.error('Invalid property favorite data:', data);
          return;
        }

        // Get property owner
        const propertyResult = await query(
          'SELECT owner_id, name FROM kost_properties WHERE id = $1',
          [propertyId]
        );

        if (propertyResult.rows.length > 0) {
          const { owner_id, name: propertyName } = propertyResult.rows[0];

          // Get user info
          const userResult = await query(
            'SELECT full_name FROM users WHERE id = $1',
            [userId]
          );

          if (userResult.rows.length > 0) {
            const userName = userResult.rows[0].full_name;

            if (action === 'add') {
              // Create notification for property owner
              const notificationResult = await query(
                `INSERT INTO notifications (user_id, type, title, message, data)
                 VALUES ($1, 'property_favorite', $2, $3, $4)
                 RETURNING *`,
                [
                  owner_id,
                  'Properti Difavoritkan',
                  `${userName} menambahkan ${propertyName} ke favorit`,
                  JSON.stringify({ property_id: propertyId, user_id: userId })
                ]
              );

              // Send real-time notification to property owner
              io.to(`user_${owner_id}`).emit('new_notification', {
                notification: notificationResult.rows[0],
                type: 'property_favorite'
              });
            }

            // Send analytics update to owner
            const favoriteCountResult = await query(
              'SELECT COUNT(*) as total_favorites FROM favorites WHERE property_id = $1',
              [propertyId]
            );

            io.to(`user_${owner_id}`).emit('property_analytics_update', {
              property_id: propertyId,
              total_favorites: parseInt(favoriteCountResult.rows[0].total_favorites),
              latest_action: action === 'add' ? 'favorited' : 'unfavorited',
              user_name: userName
            });
          }
        }
      } catch (error) {
        console.error('Error handling property favorite:', error);
      }
    });

    // Handle user registration events (for admin notifications)
    socket.on('user_registered', async (data) => {
      try {
        const { userId, userRole } = data;

        // Validate input data
        if (!userId || !userRole) {
          console.error('Invalid user registration data:', data);
          return;
        }

        // Get user info
        const userResult = await query(
          'SELECT full_name, email FROM users WHERE id = $1',
          [userId]
        );

        if (userResult.rows.length > 0) {
          const { full_name, email } = userResult.rows[0];

          // Notify all admins about new registration
          io.to('admins').emit('new_user_registration', {
            user: {
              id: userId,
              full_name,
              email,
              role: userRole
            },
            timestamp: new Date().toISOString()
          });
        }
      } catch (error) {
        console.error('Error handling user registration:', error);
      }
    });

    // Handle property creation events (for admin notifications)
    socket.on('property_created', async (data) => {
      try {
        const { propertyId } = data;

        // Validate input data
        if (!propertyId) {
          console.error('Invalid property creation data:', data);
          return;
        }

        // Get property and owner info
        const propertyResult = await query(
          `SELECT kp.name, kp.city, u.full_name as owner_name
           FROM kost_properties kp
           JOIN users u ON kp.owner_id = u.id
           WHERE kp.id = $1`,
          [propertyId]
        );

        if (propertyResult.rows.length > 0) {
          const { name, city, owner_name } = propertyResult.rows[0];

          // Notify all admins about new property
          io.to('admins').emit('new_property_created', {
            property: {
              id: propertyId,
              name,
              city,
              owner_name
            },
            timestamp: new Date().toISOString()
          });
        }
      } catch (error) {
        console.error('Error handling property creation:', error);
      }
    });

    // Handle disconnect
    socket.on('disconnect', () => {
      console.log(`User ${socket.user.full_name} disconnected`);
    });
  });

  return io;
};

const getIO = () => {
  if (!io) {
    throw new Error('Socket.io not initialized');
  }
  return io;
};

// Helper functions for emitting events from other parts of the application
const emitToUser = (userId, event, data) => {
  if (io) {
    io.to(`user_${userId}`).emit(event, data);
  }
};

const emitToRole = (role, event, data) => {
  if (io) {
    io.to(`role_${role}`).emit(event, data);
  }
};

const emitToOwners = (event, data) => {
  if (io) {
    io.to('owners').emit(event, data);
  }
};

const emitToAdmins = (event, data) => {
  if (io) {
    io.to('admins').emit(event, data);
  }
};

module.exports = {
  initializeSocket,
  getIO,
  emitToUser,
  emitToRole,
  emitToOwners,
  emitToAdmins
};
