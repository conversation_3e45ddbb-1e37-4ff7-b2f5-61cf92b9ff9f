{"name": "kost2-server", "version": "1.0.0", "description": "KOST2 Backend API Server", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "db:migrate": "node scripts/migrate.js", "db:seed": "node scripts/seed.js"}, "keywords": ["kost", "boarding-house", "rental", "api"], "author": "KOST2 Team", "license": "MIT", "type": "commonjs", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^5.1.0", "express-rate-limit": "^7.4.1", "express-validator": "^7.2.0", "helmet": "^8.0.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "pg": "^8.16.3", "socket.io": "^4.8.1", "uuid": "^11.0.3"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^2.4.6", "@types/jest": "^30.0.0", "@types/supertest": "^6.0.3", "jest": "^29.7.0", "jsdom": "^26.1.0", "nodemon": "^3.1.10", "supertest": "^7.1.3", "vitest": "^3.2.4"}}