{"name": "kost2-server", "version": "1.0.0", "description": "KOST2 Backend API Server", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "test:watch": "jest --watch", "db:migrate": "node scripts/migrate.js", "db:seed": "node scripts/seed.js"}, "keywords": ["kost", "boarding-house", "rental", "api"], "author": "KOST2 Team", "license": "MIT", "type": "commonjs", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^5.1.0", "express-rate-limit": "^7.4.1", "express-validator": "^7.2.0", "helmet": "^8.0.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "pg": "^8.16.3", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "uuid": "^11.0.3"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^7.0.0"}}