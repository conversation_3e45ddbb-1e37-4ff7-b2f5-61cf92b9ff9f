const express = require('express');
const { body, validationResult } = require('express-validator');
const { query } = require('../config/database');
const { authenticate, authorize } = require('../middleware/auth');

const router = express.Router();

// @desc    Get admin dashboard statistics
// @route   GET /api/admin/dashboard/stats
// @access  Private (Admin only)
router.get('/dashboard/stats', authenticate, authorize('admin'), async (req, res, next) => {
  try {
    // Get total users by role
    const usersResult = await query(
      `SELECT role, COUNT(*) as count 
       FROM users 
       WHERE is_active = true 
       GROUP BY role`
    );

    // Get total properties
    const propertiesResult = await query(
      'SELECT COUNT(*) as total_properties FROM kost_properties WHERE is_active = true'
    );

    // Get properties by status
    const propertiesStatusResult = await query(
      `SELECT 
         CASE WHEN available_rooms > 0 THEN 'available' ELSE 'full' END as status,
         COUNT(*) as count
       FROM kost_properties 
       WHERE is_active = true 
       GROUP BY CASE WHEN available_rooms > 0 THEN 'available' ELSE 'full' END`
    );

    // Get monthly registrations
    const monthlyRegistrationsResult = await query(
      `SELECT COUNT(*) as monthly_registrations 
       FROM users 
       WHERE created_at >= NOW() - INTERVAL '30 days'`
    );

    // Get monthly property additions
    const monthlyPropertiesResult = await query(
      `SELECT COUNT(*) as monthly_properties 
       FROM kost_properties 
       WHERE created_at >= NOW() - INTERVAL '30 days' AND is_active = true`
    );

    // Get total views and favorites
    const viewsResult = await query('SELECT COUNT(*) as total_views FROM property_views');
    const favoritesResult = await query('SELECT COUNT(*) as total_favorites FROM favorites');

    // Get recent activity (last 7 days)
    const activityResult = await query(
      `SELECT DATE(created_at) as date, COUNT(*) as registrations
       FROM users 
       WHERE created_at >= NOW() - INTERVAL '7 days'
       GROUP BY DATE(created_at)
       ORDER BY date`
    );

    // Process users by role
    const usersByRole = {};
    usersResult.rows.forEach(row => {
      usersByRole[row.role] = parseInt(row.count);
    });

    // Process properties by status
    const propertiesByStatus = {};
    propertiesStatusResult.rows.forEach(row => {
      propertiesByStatus[row.status] = parseInt(row.count);
    });

    res.json({
      success: true,
      data: {
        users: {
          total: Object.values(usersByRole).reduce((sum, count) => sum + count, 0),
          by_role: usersByRole,
          monthly_registrations: parseInt(monthlyRegistrationsResult.rows[0].monthly_registrations)
        },
        properties: {
          total: parseInt(propertiesResult.rows[0].total_properties),
          by_status: propertiesByStatus,
          monthly_additions: parseInt(monthlyPropertiesResult.rows[0].monthly_properties)
        },
        engagement: {
          total_views: parseInt(viewsResult.rows[0].total_views),
          total_favorites: parseInt(favoritesResult.rows[0].total_favorites)
        },
        activity: activityResult.rows
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get all users with pagination
// @route   GET /api/admin/users
// @access  Private (Admin only)
router.get('/users', authenticate, authorize('admin'), async (req, res, next) => {
  try {
    const { page = 1, limit = 20, role, search, status } = req.query;
    const offset = (page - 1) * limit;

    let whereConditions = [];
    let queryParams = [];
    let paramCount = 1;

    // Add role filter
    if (role && role !== 'all') {
      whereConditions.push(`role = $${paramCount}`);
      queryParams.push(role);
      paramCount++;
    }

    // Add search filter
    if (search) {
      whereConditions.push(`(full_name ILIKE $${paramCount} OR email ILIKE $${paramCount + 1})`);
      queryParams.push(`%${search}%`);
      queryParams.push(`%${search}%`);
      paramCount += 2;
    }

    // Add status filter
    if (status && status !== 'all') {
      whereConditions.push(`is_active = $${paramCount}`);
      queryParams.push(status === 'active');
      paramCount++;
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Get users
    const usersResult = await query(
      `SELECT id, email, full_name, phone, role, is_active, email_verified, created_at,
              (SELECT COUNT(*) FROM kost_properties WHERE owner_id = users.id AND is_active = true) as properties_count
       FROM users 
       ${whereClause}
       ORDER BY created_at DESC
       LIMIT $${paramCount} OFFSET $${paramCount + 1}`,
      [...queryParams, limit, offset]
    );

    // Get total count
    const countResult = await query(
      `SELECT COUNT(*) as total FROM users ${whereClause}`,
      queryParams
    );

    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: {
        users: usersResult.rows,
        pagination: {
          current_page: parseInt(page),
          total_pages: totalPages,
          total_items: total,
          items_per_page: parseInt(limit),
          has_next: page < totalPages,
          has_prev: page > 1
        }
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Update user status
// @route   PUT /api/admin/users/:id/status
// @access  Private (Admin only)
router.put('/users/:id/status', authenticate, authorize('admin'), [
  body('is_active').isBoolean().withMessage('is_active must be a boolean')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    const { id } = req.params;
    const { is_active } = req.body;

    // Check if user exists
    const userCheck = await query('SELECT id, role FROM users WHERE id = $1', [id]);
    if (userCheck.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: { message: 'User not found' }
      });
    }

    // Prevent admin from deactivating themselves
    if (id === req.user.id && !is_active) {
      return res.status(400).json({
        success: false,
        error: { message: 'Cannot deactivate your own account' }
      });
    }

    // Update user status
    const result = await query(
      'UPDATE users SET is_active = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 RETURNING *',
      [is_active, id]
    );

    res.json({
      success: true,
      data: {
        user: result.rows[0],
        message: `User ${is_active ? 'activated' : 'deactivated'} successfully`
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get all properties with moderation info
// @route   GET /api/admin/properties
// @access  Private (Admin only)
router.get('/properties', authenticate, authorize('admin'), async (req, res, next) => {
  try {
    const { page = 1, limit = 20, status, city, property_type } = req.query;
    const offset = (page - 1) * limit;

    let whereConditions = ['kp.is_active = true'];
    let queryParams = [];
    let paramCount = 1;

    // Add status filter
    if (status && status !== 'all') {
      if (status === 'available') {
        whereConditions.push('kp.available_rooms > 0');
      } else if (status === 'full') {
        whereConditions.push('kp.available_rooms = 0');
      }
    }

    // Add city filter
    if (city) {
      whereConditions.push(`kp.city ILIKE $${paramCount}`);
      queryParams.push(`%${city}%`);
      paramCount++;
    }

    // Add property type filter
    if (property_type && property_type !== 'all') {
      whereConditions.push(`kp.property_type = $${paramCount}`);
      queryParams.push(property_type);
      paramCount++;
    }

    const whereClause = whereConditions.join(' AND ');

    // Get properties with owner info
    const propertiesResult = await query(
      `SELECT kp.*, u.full_name as owner_name, u.email as owner_email,
              MIN(r.price_monthly) as min_price, MAX(r.price_monthly) as max_price,
              pi.image_url as main_image,
              COALESCE(AVG(t.rating), 0) as avg_rating,
              COUNT(DISTINCT t.id) as review_count,
              COUNT(DISTINCT f.id) as favorites_count,
              COUNT(DISTINCT pv.id) as views_count
       FROM kost_properties kp
       JOIN users u ON kp.owner_id = u.id
       LEFT JOIN rooms r ON kp.id = r.property_id
       LEFT JOIN property_images pi ON kp.id = pi.property_id AND pi.image_type = 'main' AND pi.sort_order = 0
       LEFT JOIN testimonials t ON kp.id = t.property_id AND t.is_approved = true
       LEFT JOIN favorites f ON kp.id = f.property_id
       LEFT JOIN property_views pv ON kp.id = pv.property_id
       WHERE ${whereClause}
       GROUP BY kp.id, u.full_name, u.email, pi.image_url
       ORDER BY kp.created_at DESC
       LIMIT $${paramCount} OFFSET $${paramCount + 1}`,
      [...queryParams, limit, offset]
    );

    // Get total count
    const countResult = await query(
      `SELECT COUNT(*) as total FROM kost_properties kp WHERE ${whereClause}`,
      queryParams
    );

    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: {
        properties: propertiesResult.rows,
        pagination: {
          current_page: parseInt(page),
          total_pages: totalPages,
          total_items: total,
          items_per_page: parseInt(limit),
          has_next: page < totalPages,
          has_prev: page > 1
        }
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Delete property (admin)
// @route   DELETE /api/admin/properties/:id
// @access  Private (Admin only)
router.delete('/properties/:id', authenticate, authorize('admin'), async (req, res, next) => {
  try {
    const { id } = req.params;

    // Check if property exists
    const propertyCheck = await query('SELECT id FROM kost_properties WHERE id = $1', [id]);
    if (propertyCheck.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: { message: 'Property not found' }
      });
    }

    // Soft delete by setting is_active to false
    await query(
      'UPDATE kost_properties SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1',
      [id]
    );

    res.json({
      success: true,
      data: {
        message: 'Property deleted successfully'
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get system analytics
// @route   GET /api/admin/analytics
// @access  Private (Admin only)
router.get('/analytics', authenticate, authorize('admin'), async (req, res, next) => {
  try {
    const { period = '30d' } = req.query;
    
    let interval;
    switch (period) {
      case '7d':
        interval = '7 days';
        break;
      case '90d':
        interval = '90 days';
        break;
      default:
        interval = '30 days';
    }

    // User registration trends
    const userTrendsResult = await query(
      `SELECT DATE(created_at) as date, COUNT(*) as registrations
       FROM users 
       WHERE created_at >= NOW() - INTERVAL '${interval}'
       GROUP BY DATE(created_at)
       ORDER BY date`
    );

    // Property addition trends
    const propertyTrendsResult = await query(
      `SELECT DATE(created_at) as date, COUNT(*) as properties
       FROM kost_properties 
       WHERE created_at >= NOW() - INTERVAL '${interval}' AND is_active = true
       GROUP BY DATE(created_at)
       ORDER BY date`
    );

    // Popular cities
    const popularCitiesResult = await query(
      `SELECT city, COUNT(*) as property_count
       FROM kost_properties 
       WHERE is_active = true
       GROUP BY city
       ORDER BY property_count DESC
       LIMIT 10`
    );

    // Property type distribution
    const propertyTypesResult = await query(
      `SELECT property_type, COUNT(*) as count
       FROM kost_properties 
       WHERE is_active = true
       GROUP BY property_type`
    );

    res.json({
      success: true,
      data: {
        user_trends: userTrendsResult.rows,
        property_trends: propertyTrendsResult.rows,
        popular_cities: popularCitiesResult.rows,
        property_types: propertyTypesResult.rows,
        period
      }
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
