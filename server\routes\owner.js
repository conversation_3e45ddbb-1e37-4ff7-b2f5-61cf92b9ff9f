const express = require('express');
const { body, validationResult } = require('express-validator');
const { query, transaction } = require('../config/database');
const { authenticate, authorize } = require('../middleware/auth');

const router = express.Router();

// @desc    Get owner's properties
// @route   GET /api/owner/properties
// @access  Private (Owner, Admin)
router.get('/properties', authenticate, authorize('owner', 'admin'), async (req, res, next) => {
  try {
    const { page = 1, limit = 12 } = req.query;
    const offset = (page - 1) * limit;

    const result = await query(
      `SELECT kp.*, 
              MIN(r.price_monthly) as min_price, 
              MAX(r.price_monthly) as max_price,
              pi.image_url as main_image,
              COALESCE(AVG(t.rating), 0) as avg_rating,
              COUNT(DISTINCT t.id) as review_count
       FROM kost_properties kp
       LEFT JOIN rooms r ON kp.id = r.property_id
       LEFT JOIN property_images pi ON kp.id = pi.property_id AND pi.image_type = 'main' AND pi.sort_order = 0
       LEFT JOIN testimonials t ON kp.id = t.property_id AND t.is_approved = true
       WHERE kp.owner_id = $1
       GROUP BY kp.id, pi.image_url
       ORDER BY kp.created_at DESC
       LIMIT $2 OFFSET $3`,
      [req.user.id, limit, offset]
    );

    // Get total count
    const countResult = await query(
      'SELECT COUNT(*) as total FROM kost_properties WHERE owner_id = $1',
      [req.user.id]
    );

    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: {
        properties: result.rows,
        pagination: {
          current_page: parseInt(page),
          total_pages: totalPages,
          total_items: total,
          items_per_page: parseInt(limit),
          has_next: page < totalPages,
          has_prev: page > 1
        }
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Create new property
// @route   POST /api/owner/properties
// @access  Private (Owner, Admin)
router.post('/properties', authenticate, authorize('owner', 'admin'), [
  body('name').trim().isLength({ min: 3 }).withMessage('Property name must be at least 3 characters'),
  body('description').trim().isLength({ min: 10 }).withMessage('Description must be at least 10 characters'),
  body('address').trim().isLength({ min: 5 }).withMessage('Address must be at least 5 characters'),
  body('city').trim().isLength({ min: 2 }).withMessage('City must be at least 2 characters'),
  body('province').trim().isLength({ min: 2 }).withMessage('Province must be at least 2 characters'),
  body('property_type').isIn(['putra', 'putri', 'campur']).withMessage('Invalid property type'),
  body('total_rooms').isInt({ min: 1 }).withMessage('Total rooms must be at least 1')
], async (req, res, next) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    const {
      name,
      description,
      address,
      city,
      province,
      postal_code,
      latitude,
      longitude,
      property_type,
      total_rooms
    } = req.body;

    const result = await query(
      `INSERT INTO kost_properties (
        owner_id, name, description, address, city, province, postal_code,
        latitude, longitude, property_type, total_rooms, available_rooms
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $11)
      RETURNING *`,
      [
        req.user.id, name, description, address, city, province, postal_code,
        latitude, longitude, property_type, total_rooms
      ]
    );

    res.status(201).json({
      success: true,
      data: {
        property: result.rows[0]
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Update property
// @route   PUT /api/owner/properties/:id
// @access  Private (Owner, Admin)
router.put('/properties/:id', authenticate, authorize('owner', 'admin'), [
  body('name').optional().trim().isLength({ min: 3 }).withMessage('Property name must be at least 3 characters'),
  body('description').optional().trim().isLength({ min: 10 }).withMessage('Description must be at least 10 characters'),
  body('address').optional().trim().isLength({ min: 5 }).withMessage('Address must be at least 5 characters'),
  body('city').optional().trim().isLength({ min: 2 }).withMessage('City must be at least 2 characters'),
  body('province').optional().trim().isLength({ min: 2 }).withMessage('Province must be at least 2 characters'),
  body('property_type').optional().isIn(['putra', 'putri', 'campur']).withMessage('Invalid property type'),
  body('total_rooms').optional().isInt({ min: 1 }).withMessage('Total rooms must be at least 1')
], async (req, res, next) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    const { id } = req.params;

    // Check if property exists and belongs to owner
    const propertyCheck = await query(
      'SELECT id FROM kost_properties WHERE id = $1 AND owner_id = $2',
      [id, req.user.id]
    );

    if (propertyCheck.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: { message: 'Property not found or access denied' }
      });
    }

    // Build update query dynamically
    const updates = {};
    const values = [];
    let paramCount = 1;

    const allowedFields = [
      'name', 'description', 'address', 'city', 'province', 'postal_code',
      'latitude', 'longitude', 'property_type', 'total_rooms', 'available_rooms'
    ];

    allowedFields.forEach(field => {
      if (req.body[field] !== undefined) {
        updates[field] = `$${paramCount}`;
        values.push(req.body[field]);
        paramCount++;
      }
    });

    if (Object.keys(updates).length === 0) {
      return res.status(400).json({
        success: false,
        error: { message: 'No fields to update' }
      });
    }

    values.push(id, req.user.id);
    const setClause = Object.keys(updates).map(key => `${key} = ${updates[key]}`).join(', ');

    const result = await query(
      `UPDATE kost_properties 
       SET ${setClause}, updated_at = CURRENT_TIMESTAMP 
       WHERE id = $${paramCount} AND owner_id = $${paramCount + 1}
       RETURNING *`,
      values
    );

    res.json({
      success: true,
      data: {
        property: result.rows[0]
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Delete property
// @route   DELETE /api/owner/properties/:id
// @access  Private (Owner, Admin)
router.delete('/properties/:id', authenticate, authorize('owner', 'admin'), async (req, res, next) => {
  try {
    const { id } = req.params;

    // Check if property exists and belongs to owner
    const propertyCheck = await query(
      'SELECT id FROM kost_properties WHERE id = $1 AND owner_id = $2',
      [id, req.user.id]
    );

    if (propertyCheck.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: { message: 'Property not found or access denied' }
      });
    }

    // Soft delete by setting is_active to false
    await query(
      'UPDATE kost_properties SET is_active = false, updated_at = CURRENT_TIMESTAMP WHERE id = $1',
      [id]
    );

    res.json({
      success: true,
      data: {
        message: 'Property deleted successfully'
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get property statistics
// @route   GET /api/owner/properties/:id/statistics
// @access  Private (Owner, Admin)
router.get('/properties/:id/statistics', authenticate, authorize('owner', 'admin'), async (req, res, next) => {
  try {
    const { id } = req.params;

    // Check if property exists and belongs to owner
    const propertyCheck = await query(
      'SELECT id FROM kost_properties WHERE id = $1 AND owner_id = $2',
      [id, req.user.id]
    );

    if (propertyCheck.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: { message: 'Property not found or access denied' }
      });
    }

    // Get total views
    const viewsResult = await query(
      'SELECT COUNT(*) as total_views FROM property_views WHERE property_id = $1',
      [id]
    );

    // Get monthly views (last 30 days)
    const monthlyViewsResult = await query(
      `SELECT COUNT(*) as monthly_views 
       FROM property_views 
       WHERE property_id = $1 AND created_at >= NOW() - INTERVAL '30 days'`,
      [id]
    );

    // Get total favorites
    const favoritesResult = await query(
      'SELECT COUNT(*) as total_favorites FROM favorites WHERE property_id = $1',
      [id]
    );

    // Get monthly favorites (last 30 days)
    const monthlyFavoritesResult = await query(
      `SELECT COUNT(*) as monthly_favorites 
       FROM favorites 
       WHERE property_id = $1 AND created_at >= NOW() - INTERVAL '30 days'`,
      [id]
    );

    // Get view history (last 30 days)
    const viewHistoryResult = await query(
      `SELECT DATE(created_at) as date, COUNT(*) as views
       FROM property_views 
       WHERE property_id = $1 AND created_at >= NOW() - INTERVAL '30 days'
       GROUP BY DATE(created_at)
       ORDER BY date`,
      [id]
    );

    res.json({
      success: true,
      data: {
        total_views: parseInt(viewsResult.rows[0].total_views),
        monthly_views: parseInt(monthlyViewsResult.rows[0].monthly_views),
        total_favorites: parseInt(favoritesResult.rows[0].total_favorites),
        monthly_favorites: parseInt(monthlyFavoritesResult.rows[0].monthly_favorites),
        view_history: viewHistoryResult.rows
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get owner dashboard statistics
// @route   GET /api/owner/dashboard/stats
// @access  Private (Owner, Admin)
router.get('/dashboard/stats', authenticate, authorize('owner', 'admin'), async (req, res, next) => {
  try {
    // Get total properties
    const propertiesResult = await query(
      'SELECT COUNT(*) as total_properties FROM kost_properties WHERE owner_id = $1 AND is_active = true',
      [req.user.id]
    );

    // Get total available rooms
    const roomsResult = await query(
      'SELECT SUM(available_rooms) as total_available_rooms FROM kost_properties WHERE owner_id = $1 AND is_active = true',
      [req.user.id]
    );

    // Get total views for all properties
    const viewsResult = await query(
      `SELECT COUNT(*) as total_views 
       FROM property_views pv
       JOIN kost_properties kp ON pv.property_id = kp.id
       WHERE kp.owner_id = $1`,
      [req.user.id]
    );

    // Get total favorites for all properties
    const favoritesResult = await query(
      `SELECT COUNT(*) as total_favorites 
       FROM favorites f
       JOIN kost_properties kp ON f.property_id = kp.id
       WHERE kp.owner_id = $1`,
      [req.user.id]
    );

    res.json({
      success: true,
      data: {
        total_properties: parseInt(propertiesResult.rows[0].total_properties),
        total_available_rooms: parseInt(roomsResult.rows[0].total_available_rooms || 0),
        total_views: parseInt(viewsResult.rows[0].total_views),
        total_favorites: parseInt(favoritesResult.rows[0].total_favorites)
      }
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
