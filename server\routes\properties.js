const express = require('express');
const { validationResult } = require('express-validator');
const { query } = require('../config/database');
const { optionalAuth } = require('../middleware/auth');

const router = express.Router();

// @desc    Get all properties (with limited info for guests)
// @route   GET /api/properties
// @access  Public (limited) / Private (full)
router.get('/', optionalAuth, [
  queryValidator('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  queryValidator('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
  queryValidator('city').optional().trim().isLength({ min: 1 }).withMessage('City cannot be empty'),
  queryValidator('property_type').optional().isIn(['putra', 'putri', 'campur']).withMessage('Invalid property type'),
  queryValidator('min_price').optional().isFloat({ min: 0 }).withMessage('Min price must be a positive number'),
  queryValidator('max_price').optional().isFloat({ min: 0 }).withMessage('Max price must be a positive number'),
  queryValidator('search').optional().trim().isLength({ min: 1 }).withMessage('Search term cannot be empty')
], async (req, res, next) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    const {
      page = 1,
      limit = 12,
      city,
      property_type,
      min_price,
      max_price,
      search
    } = req.query;

    // Validate pagination parameters
    const pageNum = Math.max(1, parseInt(page) || 1);
    const limitNum = Math.min(Math.max(1, parseInt(limit) || 12), 100); // Max 100 items per page
    const offset = (pageNum - 1) * limitNum;
    const isAuthenticated = !!req.user;

    // Build WHERE clause
    let whereConditions = ['kp.is_active = true'];
    let queryParams = [];
    let paramCount = 1;

    if (city) {
      whereConditions.push(`LOWER(kp.city) LIKE LOWER($${paramCount})`);
      queryParams.push(`%${city}%`);
      paramCount++;
    }

    if (property_type) {
      whereConditions.push(`kp.property_type = $${paramCount}`);
      queryParams.push(property_type);
      paramCount++;
    }

    if (min_price) {
      whereConditions.push(`r.price_monthly >= $${paramCount}`);
      queryParams.push(min_price);
      paramCount++;
    }

    if (max_price) {
      whereConditions.push(`r.price_monthly <= $${paramCount}`);
      queryParams.push(max_price);
      paramCount++;
    }

    if (search) {
      whereConditions.push(`(
        LOWER(kp.name) LIKE LOWER($${paramCount}) OR 
        LOWER(kp.description) LIKE LOWER($${paramCount}) OR 
        LOWER(kp.address) LIKE LOWER($${paramCount})
      )`);
      queryParams.push(`%${search}%`);
      paramCount++;
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Select fields based on authentication status
    const selectFields = isAuthenticated 
      ? `
        kp.id, kp.name, kp.description, kp.address, kp.city, kp.province,
        kp.latitude, kp.longitude, kp.property_type, kp.total_rooms, kp.available_rooms,
        u.full_name as owner_name, u.phone as owner_phone, u.email as owner_email,
        MIN(r.price_monthly) as min_price, MAX(r.price_monthly) as max_price,
        pi.image_url as main_image,
        COALESCE(AVG(t.rating), 0) as avg_rating,
        COUNT(DISTINCT t.id) as review_count,
        kp.created_at
      `
      : `
        kp.id, kp.name, 
        CASE WHEN LENGTH(kp.description) > 100 THEN CONCAT(SUBSTRING(kp.description, 1, 100), '...') ELSE kp.description END as description,
        CONCAT(SUBSTRING(kp.address, 1, POSITION(',' IN kp.address) - 1), ', ', kp.city) as address,
        kp.city, kp.property_type, kp.total_rooms, kp.available_rooms,
        MIN(r.price_monthly) as min_price, MAX(r.price_monthly) as max_price,
        pi.image_url as main_image,
        COALESCE(AVG(t.rating), 0) as avg_rating,
        COUNT(DISTINCT t.id) as review_count
      `;

    // Main query
    const propertiesQuery = `
      SELECT ${selectFields}
      FROM kost_properties kp
      LEFT JOIN users u ON kp.owner_id = u.id
      LEFT JOIN rooms r ON kp.id = r.property_id
      LEFT JOIN property_images pi ON kp.id = pi.property_id AND pi.image_type = 'main' AND pi.sort_order = 0
      LEFT JOIN testimonials t ON kp.id = t.property_id AND t.is_approved = true
      ${whereClause}
      GROUP BY kp.id, u.full_name, u.phone, u.email, pi.image_url
      ORDER BY kp.created_at DESC
      LIMIT $${paramCount} OFFSET $${paramCount + 1}
    `;

    queryParams.push(limitNum, offset);

    // Count query
    const countQuery = `
      SELECT COUNT(DISTINCT kp.id) as total
      FROM kost_properties kp
      LEFT JOIN rooms r ON kp.id = r.property_id
      ${whereClause}
    `;

    const [propertiesResult, countResult] = await Promise.all([
      query(propertiesQuery, queryParams),
      query(countQuery, queryParams.slice(0, -2)) // Remove limit and offset for count
    ]);

    const properties = propertiesResult.rows;
    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    // Add facilities for each property (limited for guests)
    for (let property of properties) {
      const facilitiesQuery = `
        SELECT f.name, f.icon, f.category
        FROM facilities f
        JOIN property_facilities pf ON f.id = pf.facility_id
        WHERE pf.property_id = $1 AND pf.is_available = true
        ${isAuthenticated ? '' : 'LIMIT 3'}
        ORDER BY f.category, f.name
      `;

      const facilitiesResult = await query(facilitiesQuery, [property.id]);
      property.facilities = facilitiesResult.rows;

      // Add limited message for guests
      if (!isAuthenticated && facilitiesResult.rows.length >= 3) {
        property.facilities_limited = true;
      }
    }

    res.json({
      success: true,
      data: {
        properties,
        pagination: {
          current_page: parseInt(page),
          total_pages: totalPages,
          total_items: total,
          items_per_page: parseInt(limit),
          has_next: page < totalPages,
          has_prev: page > 1
        },
        is_authenticated: isAuthenticated,
        message: isAuthenticated ? null : 'Login to see full property details and contact information'
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get single property
// @route   GET /api/properties/:id
// @access  Public (limited) / Private (full)
router.get('/:id', optionalAuth, async (req, res, next) => {
  try {
    const { id } = req.params;
    const isAuthenticated = !!req.user;

    // Record property view
    if (req.user) {
      await query(
        'INSERT INTO property_views (property_id, user_id, ip_address, user_agent) VALUES ($1, $2, $3, $4)',
        [id, req.user.id, req.ip, req.get('User-Agent')]
      );

      // Send notification to owner (if authenticated user views)
      const ownerResult = await query(
        'SELECT owner_id FROM kost_properties WHERE id = $1',
        [id]
      );

      if (ownerResult.rows.length > 0) {
        await query(
          `INSERT INTO notifications (user_id, type, title, message, related_property_id) 
           VALUES ($1, 'view_notification', 'Property Viewed', $2, $3)`,
          [
            ownerResult.rows[0].owner_id,
            `${req.user.full_name} viewed your property`,
            id
          ]
        );
      }
    } else {
      // Record anonymous view
      await query(
        'INSERT INTO property_views (property_id, ip_address, user_agent) VALUES ($1, $2, $3)',
        [id, req.ip, req.get('User-Agent')]
      );
    }

    // Get property details
    const selectFields = isAuthenticated 
      ? `
        kp.*, u.full_name as owner_name, u.phone as owner_phone, u.email as owner_email,
        COALESCE(AVG(t.rating), 0) as avg_rating,
        COUNT(DISTINCT t.id) as review_count
      `
      : `
        kp.id, kp.name, kp.description, 
        CONCAT(SUBSTRING(kp.address, 1, POSITION(',' IN kp.address) - 1), ', ', kp.city) as address,
        kp.city, kp.province, kp.property_type, kp.total_rooms, kp.available_rooms,
        COALESCE(AVG(t.rating), 0) as avg_rating,
        COUNT(DISTINCT t.id) as review_count
      `;

    const propertyQuery = `
      SELECT ${selectFields}
      FROM kost_properties kp
      LEFT JOIN users u ON kp.owner_id = u.id
      LEFT JOIN testimonials t ON kp.id = t.property_id AND t.is_approved = true
      WHERE kp.id = $1 AND kp.is_active = true
      GROUP BY kp.id, u.full_name, u.phone, u.email
    `;

    const propertyResult = await query(propertyQuery, [id]);

    if (propertyResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: { message: 'Property not found' }
      });
    }

    const property = propertyResult.rows[0];

    // Get property images
    const imagesResult = await query(
      `SELECT image_url, image_type, alt_text, sort_order 
       FROM property_images 
       WHERE property_id = $1 
       ORDER BY sort_order, created_at`,
      [id]
    );

    property.images = isAuthenticated ? imagesResult.rows : imagesResult.rows.slice(0, 3);

    // Get facilities
    const facilitiesResult = await query(
      `SELECT f.name, f.icon, f.category, pf.description
       FROM facilities f
       JOIN property_facilities pf ON f.id = pf.facility_id
       WHERE pf.property_id = $1 AND pf.is_available = true
       ORDER BY f.category, f.name`,
      [id]
    );

    property.facilities = isAuthenticated ? facilitiesResult.rows : facilitiesResult.rows.slice(0, 5);

    // Get rooms (limited for guests)
    const roomsQuery = `
      SELECT room_number, room_type, price_monthly, size_sqm,
             has_ac, has_wifi, has_private_bathroom, has_wardrobe, has_desk, status
      FROM rooms 
      WHERE property_id = $1 
      ORDER BY room_number
      ${isAuthenticated ? '' : 'LIMIT 3'}
    `;

    const roomsResult = await query(roomsQuery, [id]);
    property.rooms = roomsResult.rows;

    // Get testimonials (limited for guests)
    if (isAuthenticated) {
      const testimonialsResult = await query(
        `SELECT t.rating, t.comment, t.created_at, u.full_name as user_name
         FROM testimonials t
         JOIN users u ON t.user_id = u.id
         WHERE t.property_id = $1 AND t.is_approved = true
         ORDER BY t.created_at DESC
         LIMIT 10`,
        [id]
      );
      property.testimonials = testimonialsResult.rows;
    }

    // Check if user has favorited this property
    if (req.user) {
      const favoriteResult = await query(
        'SELECT id FROM favorites WHERE user_id = $1 AND property_id = $2',
        [req.user.id, id]
      );
      property.is_favorited = favoriteResult.rows.length > 0;
    }

    res.json({
      success: true,
      data: {
        property,
        is_authenticated: isAuthenticated,
        message: isAuthenticated ? null : 'Login to see full details, contact information, and more photos'
      }
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
