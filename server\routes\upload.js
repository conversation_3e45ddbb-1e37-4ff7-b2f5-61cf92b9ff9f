const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const { authenticate, authorize } = require('../middleware/auth');

// File magic numbers for validation
const FILE_SIGNATURES = {
  'image/jpeg': [0xFF, 0xD8, 0xFF],
  'image/png': [0x89, 0x50, 0x4E, 0x47],
  'image/webp': [0x52, 0x49, 0x46, 0x46]
};

const router = express.Router();

// Validate file content by checking magic numbers
const validateFileContent = (filePath, mimeType) => {
  try {
    const buffer = fs.readFileSync(filePath);
    const signature = FILE_SIGNATURES[mimeType];

    if (!signature) return false;

    for (let i = 0; i < signature.length; i++) {
      if (buffer[i] !== signature[i]) {
        return false;
      }
    }

    return true;
  } catch (error) {
    return false;
  }
};

// Ensure upload directory exists
const uploadDir = process.env.UPLOAD_DIR || 'uploads';
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// Configure multer for file upload
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const subDir = path.join(uploadDir, 'properties');
    if (!fs.existsSync(subDir)) {
      fs.mkdirSync(subDir, { recursive: true });
    }
    cb(null, subDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

const fileFilter = (req, file, cb) => {
  const allowedTypes = (process.env.ALLOWED_FILE_TYPES || 'image/jpeg,image/png,image/webp').split(',');
  const allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp'];

  const fileExtension = path.extname(file.originalname).toLowerCase();

  // Check both MIME type and file extension
  if (allowedTypes.includes(file.mimetype) && allowedExtensions.includes(fileExtension)) {
    // Additional security: check for double extensions
    const fileName = file.originalname.toLowerCase();
    const suspiciousPatterns = ['.php', '.js', '.html', '.exe', '.bat', '.cmd'];

    if (suspiciousPatterns.some(pattern => fileName.includes(pattern))) {
      cb(new Error('File contains suspicious patterns'), false);
      return;
    }

    cb(null, true);
  } else {
    cb(new Error(`File type ${file.mimetype} or extension ${fileExtension} is not allowed. Allowed types: ${allowedTypes.join(', ')}`), false);
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 5 * 1024 * 1024, // 5MB default
    files: 10 // Maximum 10 files per request
  }
});

// @desc    Upload property images
// @route   POST /api/upload/property-images
// @access  Private (Owner, Admin)
router.post('/property-images', authenticate, authorize('owner', 'admin'), upload.array('images', 10), async (req, res, next) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        error: { message: 'No files uploaded' }
      });
    }

    const uploadedFiles = req.files.map(file => ({
      filename: file.filename,
      originalname: file.originalname,
      mimetype: file.mimetype,
      size: file.size,
      url: `/uploads/properties/${file.filename}`
    }));

    res.json({
      success: true,
      data: {
        message: 'Files uploaded successfully',
        files: uploadedFiles
      }
    });
  } catch (error) {
    // Clean up uploaded files if error occurs
    if (req.files) {
      req.files.forEach(file => {
        fs.unlink(file.path, (err) => {
          if (err) console.error('Error deleting file:', err);
        });
      });
    }
    next(error);
  }
});

// @desc    Upload single image
// @route   POST /api/upload/image
// @access  Private
router.post('/image', authenticate, upload.single('image'), async (req, res, next) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: { message: 'No file uploaded' }
      });
    }

    const uploadedFile = {
      filename: req.file.filename,
      originalname: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
      url: `/uploads/properties/${req.file.filename}`
    };

    res.json({
      success: true,
      data: {
        message: 'File uploaded successfully',
        file: uploadedFile
      }
    });
  } catch (error) {
    // Clean up uploaded file if error occurs
    if (req.file) {
      fs.unlink(req.file.path, (err) => {
        if (err) console.error('Error deleting file:', err);
      });
    }
    next(error);
  }
});

// @desc    Delete uploaded file
// @route   DELETE /api/upload/:filename
// @access  Private (Owner, Admin)
router.delete('/:filename', authenticate, authorize('owner', 'admin'), async (req, res, next) => {
  try {
    const { filename } = req.params;
    const filePath = path.join(uploadDir, 'properties', filename);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        error: { message: 'File not found' }
      });
    }

    // Delete file
    fs.unlink(filePath, (err) => {
      if (err) {
        console.error('Error deleting file:', err);
        return res.status(500).json({
          success: false,
          error: { message: 'Error deleting file' }
        });
      }

      res.json({
        success: true,
        data: {
          message: 'File deleted successfully'
        }
      });
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
