const express = require('express');
const { body, param, validationResult } = require('express-validator');
const { query } = require('../config/database');
const { authenticate, authorize } = require('../middleware/auth');

const router = express.Router();

// @desc    Get user favorites
// @route   GET /api/users/favorites
// @access  Private
router.get('/favorites', authenticate, async (req, res, next) => {
  try {
    const result = await query(
      `SELECT kp.id, kp.name, kp.city, kp.property_type, kp.available_rooms,
              MIN(r.price_monthly) as min_price, MAX(r.price_monthly) as max_price,
              pi.image_url as main_image, f.created_at as favorited_at
       FROM favorites f
       JOIN kost_properties kp ON f.property_id = kp.id
       LEFT JOIN rooms r ON kp.id = r.property_id
       LEFT JOIN property_images pi ON kp.id = pi.property_id AND pi.image_type = 'main'
       WHERE f.user_id = $1 AND kp.is_active = true
       GROUP BY kp.id, pi.image_url, f.created_at
       ORDER BY f.created_at DESC`,
      [req.user.id]
    );

    res.json({
      success: true,
      data: {
        favorites: result.rows
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Add property to favorites
// @route   POST /api/users/favorites/:propertyId
// @access  Private
router.post('/favorites/:propertyId', [
  authenticate,
  param('propertyId').isUUID().withMessage('Invalid property ID format')
], async (req, res, next) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    const { propertyId } = req.params;

    // Check if property exists
    const propertyResult = await query(
      'SELECT id FROM kost_properties WHERE id = $1 AND is_active = true',
      [propertyId]
    );

    if (propertyResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: { message: 'Property not found' }
      });
    }

    // Check if already favorited
    const existingFavorite = await query(
      'SELECT id FROM favorites WHERE user_id = $1 AND property_id = $2',
      [req.user.id, propertyId]
    );

    if (existingFavorite.rows.length > 0) {
      return res.status(409).json({
        success: false,
        error: { message: 'Property already in favorites' }
      });
    }

    // Add to favorites
    await query(
      'INSERT INTO favorites (user_id, property_id) VALUES ($1, $2)',
      [req.user.id, propertyId]
    );

    // Send notification to property owner
    const ownerResult = await query(
      'SELECT owner_id FROM kost_properties WHERE id = $1',
      [propertyId]
    );

    if (ownerResult.rows.length > 0) {
      await query(
        `INSERT INTO notifications (user_id, type, title, message, related_property_id) 
         VALUES ($1, 'favorite_added', 'Property Favorited', $2, $3)`,
        [
          ownerResult.rows[0].owner_id,
          `${req.user.full_name} added your property to favorites`,
          propertyId
        ]
      );
    }

    res.status(201).json({
      success: true,
      data: {
        message: 'Property added to favorites'
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Remove property from favorites
// @route   DELETE /api/users/favorites/:propertyId
// @access  Private
router.delete('/favorites/:propertyId', [
  authenticate,
  param('propertyId').isUUID().withMessage('Invalid property ID format')
], async (req, res, next) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Validation failed',
          details: errors.array()
        }
      });
    }

    const { propertyId } = req.params;

    const result = await query(
      'DELETE FROM favorites WHERE user_id = $1 AND property_id = $2 RETURNING id',
      [req.user.id, propertyId]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: { message: 'Favorite not found' }
      });
    }

    res.json({
      success: true,
      data: {
        message: 'Property removed from favorites'
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get user notifications
// @route   GET /api/users/notifications
// @access  Private
router.get('/notifications', authenticate, async (req, res, next) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const offset = (page - 1) * limit;

    const result = await query(
      `SELECT n.id, n.type, n.title, n.message, n.is_read, n.created_at,
              kp.name as property_name
       FROM notifications n
       LEFT JOIN kost_properties kp ON n.related_property_id = kp.id
       WHERE n.user_id = $1
       ORDER BY n.created_at DESC
       LIMIT $2 OFFSET $3`,
      [req.user.id, limit, offset]
    );

    // Get unread count
    const unreadResult = await query(
      'SELECT COUNT(*) as unread_count FROM notifications WHERE user_id = $1 AND is_read = false',
      [req.user.id]
    );

    res.json({
      success: true,
      data: {
        notifications: result.rows,
        unread_count: parseInt(unreadResult.rows[0].unread_count)
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Mark notification as read
// @route   PUT /api/users/notifications/:id/read
// @access  Private
router.put('/notifications/:id/read', authenticate, async (req, res, next) => {
  try {
    const { id } = req.params;

    const result = await query(
      'UPDATE notifications SET is_read = true WHERE id = $1 AND user_id = $2 RETURNING id',
      [id, req.user.id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: { message: 'Notification not found' }
      });
    }

    res.json({
      success: true,
      data: {
        message: 'Notification marked as read'
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Mark all notifications as read
// @route   PUT /api/users/notifications/read-all
// @access  Private
router.put('/notifications/read-all', authenticate, async (req, res, next) => {
  try {
    await query(
      'UPDATE notifications SET is_read = true WHERE user_id = $1 AND is_read = false',
      [req.user.id]
    );

    res.json({
      success: true,
      data: {
        message: 'All notifications marked as read'
      }
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
