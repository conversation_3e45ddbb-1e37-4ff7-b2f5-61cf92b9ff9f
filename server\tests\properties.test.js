const request = require('supertest');
const express = require('express');
const cors = require('cors');
const propertyRoutes = require('../routes/properties');

// Create test app
const app = express();
app.use(cors());
app.use(express.json());
app.use('/api/properties', propertyRoutes);

describe('Properties Routes', () => {
  let testUser;
  let testOwner;
  let testProperty;
  let userToken;
  let ownerToken;

  beforeEach(async () => {
    // Create test users
    testUser = await createTestUser({
      email: '<EMAIL>',
      role: 'user'
    });

    testOwner = await createTestUser({
      email: '<EMAIL>',
      role: 'owner'
    });

    // Create test property
    testProperty = await createTestProperty(testOwner.id, {
      name: 'Test Property for API',
      city: 'Test City'
    });

    // Generate tokens
    userToken = generateTestToken(testUser.id);
    ownerToken = generateTestToken(testOwner.id);
  });

  describe('GET /api/properties', () => {
    it('should return properties list for guests', async () => {
      const response = await request(app)
        .get('/api/properties')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.properties).toBeDefined();
      expect(response.body.data.pagination).toBeDefined();
      expect(response.body.data.is_authenticated).toBe(false);
    });

    it('should return properties list for authenticated users', async () => {
      const response = await authenticatedRequest(request(app), userToken)
        .get('/api/properties')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.properties).toBeDefined();
      expect(response.body.data.pagination).toBeDefined();
      expect(response.body.data.is_authenticated).toBe(true);
    });

    it('should filter properties by city', async () => {
      const response = await request(app)
        .get('/api/properties')
        .query({ city: 'Test City' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.properties).toBeDefined();
      
      // Check if returned properties match the city filter
      response.body.data.properties.forEach(property => {
        expect(property.city.toLowerCase()).toContain('test city'.toLowerCase());
      });
    });

    it('should filter properties by property type', async () => {
      const response = await request(app)
        .get('/api/properties')
        .query({ property_type: 'campur' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.properties).toBeDefined();
      
      // Check if returned properties match the type filter
      response.body.data.properties.forEach(property => {
        expect(property.property_type).toBe('campur');
      });
    });

    it('should filter properties by price range', async () => {
      const response = await request(app)
        .get('/api/properties')
        .query({ min_price: 500000, max_price: 2000000 })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.properties).toBeDefined();
    });

    it('should search properties by name', async () => {
      const response = await request(app)
        .get('/api/properties')
        .query({ search: 'Test Property' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.properties).toBeDefined();
    });

    it('should handle pagination correctly', async () => {
      const response = await request(app)
        .get('/api/properties')
        .query({ page: 1, limit: 5 })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.pagination.current_page).toBe(1);
      expect(response.body.data.pagination.items_per_page).toBe(5);
      expect(response.body.data.properties.length).toBeLessThanOrEqual(5);
    });
  });

  describe('GET /api/properties/:id', () => {
    it('should return property details for guests', async () => {
      const response = await request(app)
        .get(`/api/properties/${testProperty.id}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.property.id).toBe(testProperty.id);
      expect(response.body.data.property.name).toBe(testProperty.name);
      expect(response.body.data.is_authenticated).toBe(false);
    });

    it('should return property details for authenticated users', async () => {
      const response = await authenticatedRequest(request(app), userToken)
        .get(`/api/properties/${testProperty.id}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.property.id).toBe(testProperty.id);
      expect(response.body.data.is_authenticated).toBe(true);
      expect(response.body.data.property.is_favorited).toBeDefined();
    });

    it('should return 404 for non-existent property', async () => {
      const response = await request(app)
        .get('/api/properties/99999')
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toBe('Property not found');
    });

    it('should return 400 for invalid property ID format', async () => {
      const response = await request(app)
        .get('/api/properties/invalid-id')
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toBe('Invalid property ID');
    });
  });

  describe('POST /api/properties/:id/view', () => {
    it('should record property view for authenticated users', async () => {
      const response = await authenticatedRequest(request(app), userToken)
        .post(`/api/properties/${testProperty.id}/view`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.message).toBe('Property view recorded');
    });

    it('should fail for unauthenticated users', async () => {
      const response = await request(app)
        .post(`/api/properties/${testProperty.id}/view`)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toBe('Access denied. No token provided.');
    });

    it('should return 404 for non-existent property', async () => {
      const response = await authenticatedRequest(request(app), userToken)
        .post('/api/properties/99999/view')
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toBe('Property not found');
    });

    it('should handle duplicate views gracefully', async () => {
      // Record first view
      await authenticatedRequest(request(app), userToken)
        .post(`/api/properties/${testProperty.id}/view`)
        .expect(200);

      // Record second view (should still succeed)
      const response = await authenticatedRequest(request(app), userToken)
        .post(`/api/properties/${testProperty.id}/view`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe('Property Data Validation', () => {
    it('should return properties with required fields', async () => {
      const response = await request(app)
        .get('/api/properties')
        .expect(200);

      expect(response.body.data.properties).toBeDefined();
      
      if (response.body.data.properties.length > 0) {
        const property = response.body.data.properties[0];
        
        // Check required fields
        expect(property.id).toBeDefined();
        expect(property.name).toBeDefined();
        expect(property.address).toBeDefined();
        expect(property.city).toBeDefined();
        expect(property.province).toBeDefined();
        expect(property.property_type).toBeDefined();
        expect(property.total_rooms).toBeDefined();
        expect(property.available_rooms).toBeDefined();
        expect(property.min_price).toBeDefined();
        expect(property.max_price).toBeDefined();
        expect(property.avg_rating).toBeDefined();
        expect(property.review_count).toBeDefined();
        expect(property.created_at).toBeDefined();
      }
    });

    it('should return properties with correct data types', async () => {
      const response = await request(app)
        .get('/api/properties')
        .expect(200);

      if (response.body.data.properties.length > 0) {
        const property = response.body.data.properties[0];
        
        expect(typeof property.id).toBe('string');
        expect(typeof property.name).toBe('string');
        expect(typeof property.total_rooms).toBe('number');
        expect(typeof property.available_rooms).toBe('number');
        expect(typeof property.min_price).toBe('number');
        expect(typeof property.max_price).toBe('number');
        expect(typeof property.avg_rating).toBe('number');
        expect(typeof property.review_count).toBe('number');
        expect(['putra', 'putri', 'campur']).toContain(property.property_type);
      }
    });
  });
});
