const { query } = require('../config/database');

// Test database setup
beforeAll(async () => {
  // Set test environment
  process.env.NODE_ENV = 'test';
  process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only';
  
  // Clean up test data before running tests
  await cleanupTestData();
});

afterAll(async () => {
  // Clean up test data after all tests
  await cleanupTestData();
});

// Clean up function to remove test data
async function cleanupTestData() {
  try {
    // Delete test users (cascade will handle related data)
    await query("DELETE FROM users WHERE email LIKE '%test%' OR email LIKE '%@example.com'");
    
    // Delete test properties
    await query("DELETE FROM kost_properties WHERE name LIKE '%Test%'");
    
    // Delete test notifications
    await query("DELETE FROM notifications WHERE title LIKE '%Test%'");
    
    console.log('Test data cleaned up successfully');
  } catch (error) {
    console.error('Error cleaning up test data:', error);
  }
}

// Helper function to create test user
global.createTestUser = async (userData = {}) => {
  const bcrypt = require('bcryptjs');
  const defaultUser = {
    email: '<EMAIL>',
    password: 'password123',
    full_name: 'Test User',
    role: 'user',
    is_active: true,
    email_verified: true
  };
  
  const user = { ...defaultUser, ...userData };
  const hashedPassword = await bcrypt.hash(user.password, 10);
  
  const result = await query(
    `INSERT INTO users (email, password, full_name, role, is_active, email_verified)
     VALUES ($1, $2, $3, $4, $5, $6)
     RETURNING id, email, full_name, role, is_active, email_verified, created_at`,
    [user.email, hashedPassword, user.full_name, user.role, user.is_active, user.email_verified]
  );
  
  return result.rows[0];
};

// Helper function to create test property
global.createTestProperty = async (ownerId, propertyData = {}) => {
  const defaultProperty = {
    name: 'Test Kost Property',
    description: 'A test property for testing purposes',
    address: 'Test Address 123',
    city: 'Test City',
    province: 'Test Province',
    property_type: 'campur',
    total_rooms: 10,
    available_rooms: 5
  };
  
  const property = { ...defaultProperty, ...propertyData };
  
  const result = await query(
    `INSERT INTO kost_properties (owner_id, name, description, address, city, province, property_type, total_rooms, available_rooms)
     VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
     RETURNING *`,
    [ownerId, property.name, property.description, property.address, property.city, property.province, property.property_type, property.total_rooms, property.available_rooms]
  );
  
  return result.rows[0];
};

// Helper function to generate JWT token for testing
global.generateTestToken = (userId) => {
  const jwt = require('jsonwebtoken');
  return jwt.sign({ userId }, process.env.JWT_SECRET, { expiresIn: '1h' });
};

// Helper function to make authenticated requests
global.authenticatedRequest = (request, token) => {
  return request.set('Authorization', `Bearer ${token}`);
};
