const request = require('supertest');
const express = require('express');
const cors = require('cors');
const userRoutes = require('../routes/users');

// Create test app
const app = express();
app.use(cors());
app.use(express.json());
app.use('/api/users', userRoutes);

describe('Users Routes', () => {
  let testUser;
  let testOwner;
  let testProperty;
  let userToken;

  beforeEach(async () => {
    // Create test users
    testUser = await createTestUser({
      email: '<EMAIL>',
      role: 'user'
    });

    testOwner = await createTestUser({
      email: '<EMAIL>',
      role: 'owner'
    });

    // Create test property
    testProperty = await createTestProperty(testOwner.id, {
      name: 'Test Property for Favorites',
      city: 'Favorite City'
    });

    // Generate token
    userToken = generateTestToken(testUser.id);
  });

  describe('GET /api/users/favorites', () => {
    it('should return empty favorites list for new user', async () => {
      const response = await authenticatedRequest(request(app), userToken)
        .get('/api/users/favorites')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.favorites).toBeDefined();
      expect(response.body.data.favorites).toHaveLength(0);
    });

    it('should fail without authentication', async () => {
      const response = await request(app)
        .get('/api/users/favorites')
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toBe('Access denied. No token provided.');
    });

    it('should return favorites with pagination', async () => {
      const response = await authenticatedRequest(request(app), userToken)
        .get('/api/users/favorites')
        .query({ page: 1, limit: 10 })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.pagination).toBeDefined();
      expect(response.body.data.pagination.current_page).toBe(1);
      expect(response.body.data.pagination.items_per_page).toBe(10);
    });
  });

  describe('POST /api/users/favorites/:propertyId', () => {
    it('should add property to favorites successfully', async () => {
      const response = await authenticatedRequest(request(app), userToken)
        .post(`/api/users/favorites/${testProperty.id}`)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.message).toBe('Property added to favorites');
    });

    it('should fail without authentication', async () => {
      const response = await request(app)
        .post(`/api/users/favorites/${testProperty.id}`)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toBe('Access denied. No token provided.');
    });

    it('should return 404 for non-existent property', async () => {
      const response = await authenticatedRequest(request(app), userToken)
        .post('/api/users/favorites/99999')
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toBe('Property not found');
    });

    it('should handle duplicate favorites gracefully', async () => {
      // Add to favorites first time
      await authenticatedRequest(request(app), userToken)
        .post(`/api/users/favorites/${testProperty.id}`)
        .expect(201);

      // Try to add again
      const response = await authenticatedRequest(request(app), userToken)
        .post(`/api/users/favorites/${testProperty.id}`)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toBe('Property already in favorites');
    });

    it('should return 400 for invalid property ID', async () => {
      const response = await authenticatedRequest(request(app), userToken)
        .post('/api/users/favorites/invalid-id')
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toBe('Invalid property ID');
    });
  });

  describe('DELETE /api/users/favorites/:propertyId', () => {
    beforeEach(async () => {
      // Add property to favorites before each test
      await authenticatedRequest(request(app), userToken)
        .post(`/api/users/favorites/${testProperty.id}`)
        .expect(201);
    });

    it('should remove property from favorites successfully', async () => {
      const response = await authenticatedRequest(request(app), userToken)
        .delete(`/api/users/favorites/${testProperty.id}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.message).toBe('Property removed from favorites');
    });

    it('should fail without authentication', async () => {
      const response = await request(app)
        .delete(`/api/users/favorites/${testProperty.id}`)
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toBe('Access denied. No token provided.');
    });

    it('should return 404 for non-existent property', async () => {
      const response = await authenticatedRequest(request(app), userToken)
        .delete('/api/users/favorites/99999')
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toBe('Property not found in favorites');
    });

    it('should return 404 when removing non-favorited property', async () => {
      // Remove the property first
      await authenticatedRequest(request(app), userToken)
        .delete(`/api/users/favorites/${testProperty.id}`)
        .expect(200);

      // Try to remove again
      const response = await authenticatedRequest(request(app), userToken)
        .delete(`/api/users/favorites/${testProperty.id}`)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.error.message).toBe('Property not found in favorites');
    });
  });

  describe('Favorites Integration Tests', () => {
    it('should show favorited property in favorites list', async () => {
      // Add property to favorites
      await authenticatedRequest(request(app), userToken)
        .post(`/api/users/favorites/${testProperty.id}`)
        .expect(201);

      // Check favorites list
      const response = await authenticatedRequest(request(app), userToken)
        .get('/api/users/favorites')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.favorites).toHaveLength(1);
      expect(response.body.data.favorites[0].id).toBe(testProperty.id);
      expect(response.body.data.favorites[0].name).toBe(testProperty.name);
    });

    it('should not show removed property in favorites list', async () => {
      // Add property to favorites
      await authenticatedRequest(request(app), userToken)
        .post(`/api/users/favorites/${testProperty.id}`)
        .expect(201);

      // Remove property from favorites
      await authenticatedRequest(request(app), userToken)
        .delete(`/api/users/favorites/${testProperty.id}`)
        .expect(200);

      // Check favorites list
      const response = await authenticatedRequest(request(app), userToken)
        .get('/api/users/favorites')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.favorites).toHaveLength(0);
    });

    it('should handle multiple favorites correctly', async () => {
      // Create another test property
      const secondProperty = await createTestProperty(testOwner.id, {
        name: 'Second Test Property',
        city: 'Another City'
      });

      // Add both properties to favorites
      await authenticatedRequest(request(app), userToken)
        .post(`/api/users/favorites/${testProperty.id}`)
        .expect(201);

      await authenticatedRequest(request(app), userToken)
        .post(`/api/users/favorites/${secondProperty.id}`)
        .expect(201);

      // Check favorites list
      const response = await authenticatedRequest(request(app), userToken)
        .get('/api/users/favorites')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.favorites).toHaveLength(2);
      
      const favoriteIds = response.body.data.favorites.map(fav => fav.id);
      expect(favoriteIds).toContain(testProperty.id);
      expect(favoriteIds).toContain(secondProperty.id);
    });
  });
});
